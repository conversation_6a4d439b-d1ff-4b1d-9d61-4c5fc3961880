import 'package:flutter/material.dart';
// import '../../Milk Records/services/milk_service.dart'; // Removed unused local service
// Keep for potential other cattle data if needed
import '../../Milk Records/services/milk_handler.dart'; // Added MilkHandler
import '../../Milk Records/models/milk_record_isar.dart';
import '../../Milk Records/dialogs/milk_form_dialog.dart';
import '../models/cattle_isar.dart';
import '../../../widgets/empty_state.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';
import '../../../utils/message_utils.dart';
import '../../../services/streams/stream_service.dart';
import 'package:get_it/get_it.dart';
import 'dart:math' as math;
import 'dart:async';

// Helper class for date range calculations
class DateRange {
  final DateTime start;
  final DateTime end;

  DateRange(this.start, this.end);

  bool contains(DateTime date) {
    return !date.isBefore(start) && date.isBefore(end);
  }

  Duration get duration => end.difference(start);

  @override
  String toString() => 'DateRange(${start.toIso8601String()} - ${end.toIso8601String()})';
}

// Enhanced summary statistics
class MilkSummaryStats {
  final double morningAverage;
  final double eveningAverage;
  final double totalAverage;
  final double totalProduction;
  final double maxDaily;
  final double minDaily;
  final int recordCount;
  final double trend; // Percentage change from previous period
  final List<double> weeklyAverages;

  MilkSummaryStats({
    required this.morningAverage,
    required this.eveningAverage,
    required this.totalAverage,
    required this.totalProduction,
    required this.maxDaily,
    required this.minDaily,
    required this.recordCount,
    required this.trend,
    required this.weeklyAverages,
  });
}

class MilkTab extends StatefulWidget {
  final CattleIsar cattle;
  final bool
      recordsSelected; // Keep this if it's used for initial tab selection
  final MilkHandler milkHandler; // Add MilkHandler parameter for dependency injection

  const MilkTab({
    Key? key,
    required this.cattle,
    this.recordsSelected = false,
    required this.milkHandler, // Make it required
  }) : super(key: key);

  @override
  State<MilkTab> createState() => _MilkTabState();
}

class _MilkTabState extends State<MilkTab> with SingleTickerProviderStateMixin {
  late TabController _tabController; // Made non-nullable
  // final MilkService _milkService = MilkService(); // Removed local service
  late final MilkHandler _milkHandler; // Use MilkHandler
  List<MilkRecordIsar> _records = [];

  bool _isLoading = true;
  String _selectedTimeRange = 'Today';
  // final bool _isDbInitialized = true; // Removed redundant flag
  String? _initializationError;
  bool _isMounted = false;

  // Cache for trend data
  List<FlSpot>? _spots;
  String? _lastTimeRange;
  DateTime? _lastUpdateTime;

  // Automatic Status variables
  String currentStatus = 'No Records';
  IconData statusIcon = Icons.info_outline;
  Color statusColor = Colors.grey;

  // Stream subscription for real-time updates
  StreamSubscription<Map<String, dynamic>>? _milkStreamSubscription;

  @override
  void initState() {
    super.initState();
    _isMounted = true;

    // Initialize TabController synchronously
    _tabController = TabController(
      length: 3, // Summary, Records, Alerts
      vsync: this,
      initialIndex: widget.recordsSelected ? 1 : 0, // Set initial index
    );
    _tabController.addListener(_handleTabChange);

    // Initialize MilkHandler from injected dependency
    _milkHandler = widget.milkHandler;

    // Start data initialization after the first frame
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_isMounted) {
        _initializeData();
        _subscribeToMilkUpdates();
      }
    });
  }

  // Removed _initializeServices function

  @override
  void didUpdateWidget(MilkTab oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Optional: If cattle object itself can change, reload data
    if (widget.cattle.businessId != oldWidget.cattle.businessId) {
      _initializeData(); // Re-initialize if the cattle context changes
    }
    // Keep tab selection logic if needed
    else if (widget.recordsSelected != oldWidget.recordsSelected) {
      _tabController.animateTo(widget.recordsSelected ? 1 : 0);
    }
  }

  @override
  void dispose() {
    _isMounted = false;
    _tabController.dispose();
    _milkStreamSubscription?.cancel();
    super.dispose();
  }

  Future<void> _initializeData() async {
    // Check for valid cattle ID at the start
    final businessId = widget.cattle.businessId;
    if (businessId == null || businessId.isEmpty) {
      debugPrint(
          'Error: Cannot initialize MilkTab data. Cattle businessId is null or empty.');
      if (_isMounted) {
        setState(() {
          _isLoading = false;
          _initializationError = 'Cannot load milk data: Invalid Cattle ID.';
        });
      }
      return;
    }

    // Don't proceed if handler initialization failed earlier
    if (_initializationError != null &&
        _initializationError!.contains('milk service')) {
      return;
    }

    if (!_isMounted) return;
    setState(() {
      _isLoading = true;
      _initializationError = null; // Clear previous errors on new attempt
    });

    try {
      await _loadRecords(); // Load initial records
      // Summary and Status are calculated within _loadRecords now

      // Periodic refresh removed as local state updates are more efficient

      if (_isMounted) {
        setState(() {
          _isLoading = false; // Set loading false only on success
        });
      }
    } catch (e, s) {
      debugPrint('Error during milk data initialization: $e\n$s');
      if (_isMounted) {
        setState(() {
          _isLoading = false;
          _initializationError = 'Failed to load milk data.';
          _records = []; // Clear potentially stale data
          _spots = null;
        });
      }
    }
    // Removed finally block
  }

  /// Subscribe to milk record updates for real-time UI updates
  void _subscribeToMilkUpdates() {
    final streamService = GetIt.instance<StreamService>();
    final cattleBusinessId = widget.cattle.businessId;

    if (cattleBusinessId == null || cattleBusinessId.isEmpty) {
      debugPrint('Cannot subscribe to milk updates: Invalid cattle business ID');
      return;
    }

    // Subscribe to milk record updates
    _milkStreamSubscription = streamService.milkStream.listen((event) {
      final eventCattleId = event['cattleId'] as String?;
      if (eventCattleId == cattleBusinessId && _isMounted) {
        debugPrint('Milk record update received for cattle: $cattleBusinessId');
        _refreshData(); // Refresh data when milk records change
      }
    }, onError: (error) {
      debugPrint('Error in milk record stream: $error');
    });
  }

  Future<void> _loadRecords() async {
    final businessId = widget.cattle.businessId;
    if (businessId == null || businessId.isEmpty) {
      throw Exception("Cannot load records without a valid Cattle ID.");
    }

    try {
      // Use MilkHandler consistently
      _records = await _milkHandler.getMilkRecordsForCattle(businessId);

      // Sort records by date in descending order
      _records.sort(
          (a, b) => (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));

      // Calculate summary and update status based on loaded records
      _calculateSummary();
      _updateStatus();
      _spots = null; // Invalidate chart cache when records change
    } catch (e) {
      debugPrint('Error loading milk records: $e');
      _records = [];
      _calculateSummary();
      _updateStatus();
      _spots = null;
      throw Exception(_getReadableErrorMessage(e));
    }
  }

  // Periodic refresh removed as local state updates are more efficient

  Future<void> _refreshData() async {
    if (!_isMounted) return;
    final businessId = widget.cattle.businessId;
    if (businessId == null || businessId.isEmpty) return;

    try {
      // Use MilkHandler consistently
      final latestRecords =
          await _milkHandler.getMilkRecordsForCattle(businessId);

      if (!_isMounted) return;

      // Update state
      setState(() {
        _records = latestRecords;
        _records.sort(
            (a, b) => (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
        _calculateSummary();
        _updateStatus();
        _spots = null;
      });
    } catch (e) {
      debugPrint('Error refreshing milk records: $e');
      if (_isMounted && mounted) {
        CattleMessageUtils.showError(context, _getReadableErrorMessage(e));
      }
    }
  }

  // --- Status Logic (Automatic) ---
  void _updateStatus() {
    String newStatus = 'No Records';
    IconData newIcon = Icons.info_outline;
    Color newColor = const Color(0xFF607D8B); // Blue Grey - for no records status

    if (_records.isNotEmpty) {
      final lastRecordDate = _records.first.date;
      if (lastRecordDate != null) {
        // Consider 'Producing' if the last record is within a reasonable timeframe (e.g., 3 days)
        if (DateTime.now().difference(lastRecordDate).inDays <= 3) {
          newStatus = 'Producing';
          newIcon = Icons.opacity; // Water drop icon
          newColor = Colors.blue; // Use theme.colorScheme.primary later
        } else {
          newStatus = 'Not Producing';
          newIcon = Icons.science_outlined; // Dry / Test tube icon
          newColor = Colors.orange; // Use theme.colorScheme.secondary later
        }
      } else {
        newStatus = 'Date Missing'; // Handle records with no date
        newIcon = Icons.warning_amber_outlined;
        newColor = Colors.amber; // Use theme warning color later
      }
    }

    // Update state only if changed
    if (newStatus != currentStatus) {
      // Use _safeSetState as this might be called from background refresh
      _safeSetState(() {
        currentStatus = newStatus;
        statusIcon = newIcon;
        statusColor = newColor; // Store the conceptual color
      });
    }
  }

  // --- Calculation Logic ---

  // Cached filtered records to avoid recalculation
  List<MilkRecordIsar>? _cachedFilteredRecords;
  String? _lastFilterTimeRange;

  // Enhanced date range calculation with caching
  DateRange _getDateRange(String timeRange) {
    DateTime now = DateTime.now();
    DateTime startDate;
    DateTime endDate = now;

    switch (timeRange) {
      case 'Today':
        startDate = DateTime(now.year, now.month, now.day);
        endDate = startDate.add(const Duration(days: 1));
        break;
      case 'Last 7 Days':
        startDate = DateTime(now.year, now.month, now.day)
            .subtract(const Duration(days: 6));
        break;
      case 'Last 30 Days':
        startDate = DateTime(now.year, now.month, now.day)
            .subtract(const Duration(days: 29));
        break;
      case 'Last 90 Days':
        startDate = DateTime(now.year, now.month, now.day)
            .subtract(const Duration(days: 89));
        break;
      case 'Last 6 Months':
        startDate = DateTime(now.year, now.month - 6, now.day);
        break;
      case 'Last Year':
        startDate = DateTime(now.year - 1, now.month, now.day);
        break;
      case 'All Time':
      default:
        startDate = _records.isNotEmpty
            ? _records.map((r) => r.date!).reduce((a, b) => a.isBefore(b) ? a : b)
            : DateTime(2000);
        break;
    }

    return DateRange(startDate, endDate);
  }

  // Optimized method to filter records by time range with caching
  List<MilkRecordIsar> _getFilteredRecords() {
    // Use cache if available and time range hasn't changed
    if (_cachedFilteredRecords != null && _lastFilterTimeRange == _selectedTimeRange) {
      return _cachedFilteredRecords!;
    }

    final dateRange = _getDateRange(_selectedTimeRange);

    List<MilkRecordIsar> filteredRecords;
    if (_selectedTimeRange == 'All Time') {
      filteredRecords = _records;
    } else {
      filteredRecords = _records.where((record) {
        return record.date != null &&
            !record.date!.isBefore(dateRange.start) &&
            record.date!.isBefore(dateRange.end);
      }).toList();
    }

    // Cache the results
    _cachedFilteredRecords = filteredRecords;
    _lastFilterTimeRange = _selectedTimeRange;

    return filteredRecords;
  }

  // Enhanced calculation with trend analysis and caching
  MilkSummaryStats? _cachedSummaryStats;
  String? _lastSummaryTimeRange;

  MilkSummaryStats _calculateEnhancedSummary() {
    // Use cache if available and time range hasn't changed
    if (_cachedSummaryStats != null && _lastSummaryTimeRange == _selectedTimeRange) {
      return _cachedSummaryStats!;
    }

    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) {
      final emptyStats = MilkSummaryStats(
        morningAverage: 0,
        eveningAverage: 0,
        totalAverage: 0,
        totalProduction: 0,
        maxDaily: 0,
        minDaily: 0,
        recordCount: 0,
        trend: 0,
        weeklyAverages: [],
      );
      _cachedSummaryStats = emptyStats;
      _lastSummaryTimeRange = _selectedTimeRange;
      return emptyStats;
    }

    // Calculate basic statistics
    double totalMorning = 0;
    double totalEvening = 0;
    double totalProduction = 0;
    int morningCount = 0;
    int eveningCount = 0;
    List<double> dailyTotals = [];

    for (var record in filteredRecords) {
      if (record.morningAmount != null && record.morningAmount! > 0) {
        totalMorning += record.morningAmount!;
        morningCount++;
      }
      if (record.eveningAmount != null && record.eveningAmount! > 0) {
        totalEvening += record.eveningAmount!;
        eveningCount++;
      }
      final dailyTotal = record.totalAmount ?? 0;
      totalProduction += dailyTotal;
      if (dailyTotal > 0) {
        dailyTotals.add(dailyTotal);
      }
    }

    // Calculate averages
    double morningAverage = morningCount > 0 ? totalMorning / morningCount : 0;
    double eveningAverage = eveningCount > 0 ? totalEvening / eveningCount : 0;
    double totalAverage = dailyTotals.isNotEmpty
        ? dailyTotals.reduce((a, b) => a + b) / dailyTotals.length
        : 0;

    // Calculate min/max
    double maxDaily = dailyTotals.isNotEmpty ? dailyTotals.reduce(math.max) : 0;
    double minDaily = dailyTotals.isNotEmpty ? dailyTotals.reduce(math.min) : 0;

    // Calculate trend (comparison with previous period)
    double trend = _calculateTrend(filteredRecords);

    // Calculate weekly averages for trend analysis
    List<double> weeklyAverages = _calculateWeeklyAverages(filteredRecords);

    final stats = MilkSummaryStats(
      morningAverage: morningAverage,
      eveningAverage: eveningAverage,
      totalAverage: totalAverage,
      totalProduction: totalProduction,
      maxDaily: maxDaily,
      minDaily: minDaily,
      recordCount: filteredRecords.length,
      trend: trend,
      weeklyAverages: weeklyAverages,
    );

    // Cache the results
    _cachedSummaryStats = stats;
    _lastSummaryTimeRange = _selectedTimeRange;

    return stats;
  }

  void _calculateSummary() {
    _calculateEnhancedSummary();

    // Summary stats are now handled by the enhanced summary system
  }

  double _calculateTrend(List<MilkRecordIsar> currentRecords) {
    if (currentRecords.length < 2) return 0;

    // Split records into two halves to compare
    final midPoint = currentRecords.length ~/ 2;
    final firstHalf = currentRecords.sublist(0, midPoint);
    final secondHalf = currentRecords.sublist(midPoint);

    double firstHalfAvg = firstHalf.isEmpty ? 0 :
        firstHalf.map((r) => r.totalAmount ?? 0).reduce((a, b) => a + b) / firstHalf.length;
    double secondHalfAvg = secondHalf.isEmpty ? 0 :
        secondHalf.map((r) => r.totalAmount ?? 0).reduce((a, b) => a + b) / secondHalf.length;

    if (firstHalfAvg == 0) return 0;
    return ((secondHalfAvg - firstHalfAvg) / firstHalfAvg) * 100;
  }

  List<double> _calculateWeeklyAverages(List<MilkRecordIsar> records) {
    if (records.isEmpty) return [];

    // Group records by week
    Map<int, List<double>> weeklyData = {};
    for (var record in records) {
      if (record.date != null && record.totalAmount != null) {
        final weekNumber = _getWeekNumber(record.date!);
        weeklyData.putIfAbsent(weekNumber, () => []).add(record.totalAmount!);
      }
    }

    // Calculate weekly averages
    return weeklyData.values
        .map((weekData) => weekData.reduce((a, b) => a + b) / weekData.length)
        .toList();
  }

  int _getWeekNumber(DateTime date) {
    final startOfYear = DateTime(date.year, 1, 1);
    final daysSinceStart = date.difference(startOfYear).inDays;
    return (daysSinceStart / 7).floor();
  }

  // Cache invalidation method
  void _invalidateCache() {
    _spots = null;
    _cachedFilteredRecords = null;
    _cachedSummaryStats = null;
    _lastTimeRange = null;
    _lastFilterTimeRange = null;
    _lastSummaryTimeRange = null;
    _lastUpdateTime = null;
  }

  // Safe setState method to avoid calling setState when widget is not mounted
  void _safeSetState(VoidCallback fn) {
    if (_isMounted && mounted) {
      setState(fn);
    }
  }

  // Helper method to get readable error messages
  String _getReadableErrorMessage(dynamic error) {
    if (error.toString().contains('connection')) {
      return 'Database connection error. Please try again.';
    } else if (error.toString().contains('timeout')) {
      return 'Request timed out. Please check your connection.';
    } else {
      return 'An unexpected error occurred. Please try again.';
    }
  }

  // Export functionality
  Future<void> _exportData() async {
    try {
      final filteredRecords = _getFilteredRecords();
      final stats = _calculateEnhancedSummary();

      if (filteredRecords.isEmpty) {
        if (mounted) {
          CattleMessageUtils.showError(context, 'No data to export for the selected time range.');
        }
        return;
      }

      // Create CSV content
      final StringBuffer csvContent = StringBuffer();

      // Add header
      csvContent.writeln('Cattle Name,Date,Morning Amount (L),Evening Amount (L),Total Amount (L),Notes');

      // Add data rows
      for (var record in filteredRecords) {
        final date = record.date != null ? DateFormat('yyyy-MM-dd').format(record.date!) : '';
        final morning = record.morningAmount?.toStringAsFixed(1) ?? '0.0';
        final evening = record.eveningAmount?.toStringAsFixed(1) ?? '0.0';
        final total = record.totalAmount?.toStringAsFixed(1) ?? '0.0';
        final notes = record.notes?.replaceAll(',', ';') ?? ''; // Replace commas to avoid CSV issues

        csvContent.writeln('${widget.cattle.name ?? 'Unknown'},$date,$morning,$evening,$total,$notes');
      }

      // Add summary at the end
      csvContent.writeln('');
      csvContent.writeln('SUMMARY ($_selectedTimeRange)');
      csvContent.writeln('Morning Average,${stats.morningAverage.toStringAsFixed(1)}');
      csvContent.writeln('Evening Average,${stats.eveningAverage.toStringAsFixed(1)}');
      csvContent.writeln('Daily Average,${stats.totalAverage.toStringAsFixed(1)}');
      csvContent.writeln('Total Production,${stats.totalProduction.toStringAsFixed(1)}');
      csvContent.writeln('Max Daily,${stats.maxDaily.toStringAsFixed(1)}');
      csvContent.writeln('Min Daily,${stats.minDaily.toStringAsFixed(1)}');
      csvContent.writeln('Record Count,${stats.recordCount}');
      csvContent.writeln('Trend,${stats.trend.toStringAsFixed(1)}%');

      // For now, just show the content in a dialog (in a real app, you'd save to file)
      if (mounted) {
        _showExportDialog(csvContent.toString());
      }
    } catch (e) {
      if (mounted) {
        CattleMessageUtils.showError(context, 'Failed to export data: $e');
      }
    }
  }

  void _showExportDialog(String csvContent) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Export Data'),
        content: SizedBox(
          width: double.maxFinite,
          height: 300,
          child: SingleChildScrollView(
            child: SelectableText(
              csvContent,
              style: const TextStyle(fontFamily: 'monospace', fontSize: 12),
            ),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              // Copy to clipboard
              // Clipboard.setData(ClipboardData(text: csvContent));
              Navigator.of(context).pop();
              CattleMessageUtils.showSuccess(context, 'Data copied to clipboard');
            },
            child: const Text('Copy'),
          ),
        ],
      ),
    );
  }

  // --- Build Methods ---

  @override
  Widget build(BuildContext context) {
    final ThemeData theme = Theme.of(context);

    // Handle Initialization Error First
    if (_initializationError != null) {
      return Scaffold(
        appBar: AppBar(
            title: Text(widget.cattle.name ?? 'Milk Production'),
            backgroundColor: theme.colorScheme.error),
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(_initializationError!,
                style: TextStyle(color: theme.colorScheme.error)),
          ),
        ),
      );
    }

    // Then Handle Loading State
    if (_isLoading) {
      return Scaffold(
        appBar: AppBar(
            title: Text(widget.cattle.name ??
                'Milk Production')), // Show AppBar even when loading
        body: const Center(child: CircularProgressIndicator()),
      );
    }

    // Main UI when loaded and no error
    return Scaffold(
      body: Column(
        children: [
          TabBar(
            controller: _tabController,
            // No global label color - we'll set it per tab
            labelColor: null,
            unselectedLabelColor: null,
            indicatorColor: _getIndicatorColor(), // Use color based on selected tab
            tabs: [
              Tab(
                icon: Icon(
                  Icons.analytics_outlined,
                  color: _tabController.index == 0
                      ? const Color(0xFF2E7D32) // Active: Green for summary
                      : const Color(0xFF2E7D32).withValues(alpha: 0.7), // Inactive: Green with opacity
                ),
                child: Text(
                  'Summary',
                  style: TextStyle(
                    color: _tabController.index == 0
                        ? const Color(0xFF2E7D32) // Active: Green for summary
                        : const Color(0xFF2E7D32).withValues(alpha: 0.7), // Inactive: Green with opacity
                    fontWeight: _tabController.index == 0
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
              ),
              Tab(
                icon: Icon(
                  Icons.list_alt_outlined,
                  color: _tabController.index == 1
                      ? const Color(0xFF1976D2) // Active: Blue for records
                      : const Color(0xFF1976D2).withValues(alpha: 0.7), // Inactive: Blue with opacity
                ),
                child: Text(
                  'Records',
                  style: TextStyle(
                    color: _tabController.index == 1
                        ? const Color(0xFF1976D2) // Active: Blue for records
                        : const Color(0xFF1976D2).withValues(alpha: 0.7), // Inactive: Blue with opacity
                    fontWeight: _tabController.index == 1
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
              ),
              Tab(
                icon: Icon(
                  Icons.notifications_outlined,
                  color: _tabController.index == 2
                      ? const Color(0xFF6A1B9A) // Active: Purple for alerts
                      : const Color(0xFF6A1B9A).withValues(alpha: 0.7), // Inactive: Purple with opacity
                ),
                child: Text(
                  'Alerts',
                  style: TextStyle(
                    color: _tabController.index == 2
                        ? const Color(0xFF6A1B9A) // Active: Purple for alerts
                        : const Color(0xFF6A1B9A).withValues(alpha: 0.7), // Inactive: Purple with opacity
                    fontWeight: _tabController.index == 2
                        ? FontWeight.bold
                        : FontWeight.normal,
                  ),
                ),
              ),
            ],
          ),
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildSummaryTab(theme),
                _buildRecordsTab(theme),
                _buildAlertsTab(theme),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddRecordDialog,
        backgroundColor: _getFabColor(),
        foregroundColor: Colors.white, // White icon for visibility
        tooltip: 'Add Milk Record',
        child: const Icon(Icons.add),
      ),
    );
  }

  void _handleTabChange() {
    // Trigger rebuild when tab changes to update tab colors and other UI elements
    if (_isMounted) {
      setState(() {
        // This will rebuild the UI with the new tab index
        // causing the tab colors to update based on _tabController.index
      });
    }
  }

  // Get the indicator color based on the selected tab
  Color _getIndicatorColor() {
    switch (_tabController.index) {
      case 0:
        return const Color(0xFF2E7D32); // Green for summary
      case 1:
        return const Color(0xFF1976D2); // Blue for records
      case 2:
        return const Color(0xFF6A1B9A); // Purple for alerts
      default:
        return const Color(0xFF2E7D32); // Default to green
    }
  }

  // Get FAB color based on the selected tab
  Color _getFabColor() {
    // Return the color matching the selected tab
    return _getIndicatorColor();
  }

  // --- Tab Building Helpers ---

  Widget _buildSummaryTab(ThemeData theme) {
    return RefreshIndicator(
      onRefresh: _refreshData, // Allow pull to refresh summary
      child: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          _buildStatusCard(theme),
          const SizedBox(height: 16),
          _buildProductionSummaryCard(theme),
          const SizedBox(height: 16),
          _buildProductionTrendCard(theme),
        ],
      ),
    );
  }

  Widget _buildRecordsTab(ThemeData theme) {
    return RefreshIndicator(
      onRefresh: _refreshData, // Allow pull to refresh records
      child: _records.isEmpty
          ? Card(
              elevation: 2,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header with gradient background
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          const Color(0xFF1976D2), // Blue for records
                          const Color(0xFF1976D2).withAlpha(179), // 0.7 * 255 = 179
                        ],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(12),
                        topRight: Radius.circular(12),
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.list_alt_outlined,
                            color: Colors.white,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Text(
                          'Milk Records',
                          style: theme.textTheme.titleMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Empty state content
                  const MilkEmptyState(
                    icon: Icons.list_alt_outlined,
                    message: 'No Milk Records',
                    subtitle: 'Add a record to start tracking milk production',
                    color: Color(0xFF1976D2), // Blue for records
                  ),
                ],
              ),
            )
          : ListView.builder(
              padding: const EdgeInsets.only(bottom: 80), // Padding for FAB
              itemCount: _records.length,
              itemBuilder: (context, index) {
                final record = _records[index];
                return _buildMilkRecordItem(record, theme);
              },
            ),
    );
  }

  Widget _buildAlertsTab(ThemeData theme) {
    // Placeholder for Alerts Tab
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with gradient background
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  const Color(0xFF6A1B9A), // Purple for alerts
                  const Color(0xFF6A1B9A).withAlpha(179), // 0.7 * 255 = 179
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.white.withAlpha(77), // 0.3 * 255 = 77
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.notifications_outlined,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Milk Alerts',
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          // Empty state content
          const MilkEmptyState(
            icon: Icons.notifications_off_outlined,
            message: 'Milk Alerts Coming Soon',
            subtitle: 'This feature will help you track milk production alerts',
            color: Color(0xFF6A1B9A), // Purple for alerts
          ),
        ],
      ),
    );
  }

  // --- Summary Tab Components ---

  Widget _buildStatusCard(ThemeData theme) {
    // Define a unique teal color for the status card
    const Color statusTeal = Color(0xFF009688);

    // Use the automatically updated status variables
    // Map status to appropriate icon and color
    IconData statusIconToUse;

    if (currentStatus == 'Producing') {
      statusIconToUse = Icons.water_drop;
    } else if (currentStatus == 'Not Producing') {
      statusIconToUse = Icons.water_drop_outlined;
    } else if (currentStatus.contains('Missing')) {
      statusIconToUse = Icons.help_outline;
    } else {
      // No Records
      statusIconToUse = Icons.info_outline;
    }

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: statusTeal.withAlpha(26),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: statusTeal.withAlpha(50),
                  child: Icon(
                    statusIconToUse,
                    color: statusTeal,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Current Production Status',
                  style: theme.textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    currentStatus,
                    style: const TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                      color: statusTeal,
                    ),
                  ),
                ),
                if (_records.isNotEmpty && _records.first.date != null)
                  Text(
                    'Last record: ${DateFormat('MMM dd, yyyy').format(_records.first.date!)}',
                    style: TextStyle(
                      fontSize: 14,
                      color: theme.colorScheme.onSurface
                          .withAlpha(153), // 0.6 * 255 = 153
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductionSummaryCard(ThemeData theme) {
    // Define unique colors for each metric - avoiding orange, yellow, and grey
    const Color cardGreen = Color(0xFF2E7D32); // Primary green for card header
    const Color morningBlue = Color(0xFF1976D2); // Deeper blue for morning
    const Color eveningPurple = Color(0xFF7B1FA2); // Deeper purple for evening
    const Color avgTeal = Color(0xFF009688); // Teal for average
    const Color totalRed = Color(0xFFD32F2F); // Red for total
    const Color trendColor = Color(0xFF3F51B5); // Indigo for trend

    final stats = _calculateEnhancedSummary();

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: cardGreen.withAlpha(26),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    CircleAvatar(
                      radius: 20,
                      backgroundColor: cardGreen.withAlpha(50),
                      child: const Icon(
                        Icons.bar_chart_rounded,
                        color: cardGreen,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Production Summary',
                      style: theme.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                // Show trend indicator
                if (stats.trend != 0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: stats.trend > 0 ? Colors.green.withAlpha(20) : Colors.red.withAlpha(20),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          stats.trend > 0 ? Icons.trending_up : Icons.trending_down,
                          size: 16,
                          color: stats.trend > 0 ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${stats.trend.abs().toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.bold,
                            color: stats.trend > 0 ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildProductionRow(
                  theme,
                  Icons.wb_sunny_rounded,
                  'Morning Average',
                  stats.morningAverage,
                  morningBlue,
                ),
                const SizedBox(height: 12),
                _buildProductionRow(
                  theme,
                  Icons.nightlight_rounded,
                  'Evening Average',
                  stats.eveningAverage,
                  eveningPurple,
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(vertical: 12.0),
                  child: Divider(height: 1),
                ),
                _buildProductionRow(
                  theme,
                  Icons.calculate_rounded,
                  'Daily Average',
                  stats.totalAverage,
                  avgTeal,
                  isTotal: true,
                ),
                const SizedBox(height: 12),
                _buildProductionRow(
                  theme,
                  Icons.show_chart_rounded,
                  'Total Production',
                  stats.totalProduction,
                  totalRed,
                  isTotal: true,
                ),
                // Add min/max if there are records
                if (stats.recordCount > 1) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: _buildProductionRow(
                          theme,
                          Icons.keyboard_arrow_down,
                          'Min Daily',
                          stats.minDaily,
                          Colors.blue,
                          isCompact: true,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: _buildProductionRow(
                          theme,
                          Icons.keyboard_arrow_up,
                          'Max Daily',
                          stats.maxDaily,
                          Colors.green,
                          isCompact: true,
                        ),
                      ),
                    ],
                  ),
                ],
                // Show record count and export button
                if (stats.recordCount > 0) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: trendColor.withAlpha(15),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              const Icon(Icons.receipt_long, size: 16),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '${stats.recordCount} records in ${_selectedTimeRange.toLowerCase()}',
                                  style: const TextStyle(
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Export button
                      Container(
                        decoration: BoxDecoration(
                          color: cardGreen.withAlpha(15),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: IconButton(
                          onPressed: _exportData,
                          icon: const Icon(Icons.download, size: 20),
                          tooltip: 'Export Data',
                          padding: const EdgeInsets.all(8),
                          constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
                        ),
                      ),
                    ],
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductionRow(
      ThemeData theme, IconData icon, String label, double? value, Color color,
      {bool isTotal = false, bool isCompact = false}) {
    final displayValue = value?.toStringAsFixed(1) ?? '0.0';

    // Enhanced text styles with better typography
    final labelStyle = TextStyle(
      fontSize: isCompact ? 11 : (isTotal ? 16 : 14),
      fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
      color: isTotal
          ? color
          : theme.colorScheme.onSurface.withAlpha(204), // 0.8 * 255 = 204
    );

    final valueStyle = TextStyle(
      fontSize: isCompact ? 12 : (isTotal ? 16 : 14),
      fontWeight: isTotal ? FontWeight.w600 : FontWeight.w500,
      color: isTotal ? color : theme.colorScheme.onSurface,
    );

    final unitStyle = TextStyle(
      fontSize: isCompact ? 10 : (isTotal ? 14 : 12),
      fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
      color: isTotal
          ? color
          : theme.colorScheme.onSurface.withAlpha(179), // 0.7 * 255 = 179
    );

    return Container(
      padding: EdgeInsets.symmetric(
        vertical: isCompact ? 4.0 : 8.0,
        horizontal: isCompact ? 2.0 : 4.0
      ),
      decoration: isTotal
          ? BoxDecoration(
              color: color.withAlpha(15),
              borderRadius: BorderRadius.circular(8),
            )
          : null,
      child: isCompact
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  padding: const EdgeInsets.all(6),
                  decoration: BoxDecoration(
                    color: color.withAlpha(20),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: const Icon(Icons.opacity, size: 16),
                ),
                const SizedBox(height: 4),
                Text(
                  label,
                  style: labelStyle,
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(displayValue, style: valueStyle),
                    const SizedBox(width: 2),
                    Text('L', style: unitStyle),
                  ],
                ),
              ],
            )
          : Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withAlpha(isTotal ? 40 : 20),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Icon(Icons.opacity, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    label,
                    style: labelStyle,
                  ),
                ),
                Text(displayValue, style: valueStyle),
                const SizedBox(width: 4),
                Text('L', style: unitStyle), // Unit
              ],
            ),
    );
  }

  Widget _buildProductionTrendCard(ThemeData theme) {
    // Use a unique indigo color for the trend card
    const Color chartIndigo = Color(0xFF3F51B5);

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: chartIndigo.withAlpha(26),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: chartIndigo.withAlpha(50),
                  child: const Icon(
                    Icons.timeline_rounded,
                    color: chartIndigo,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'Production Trend',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                const SizedBox(width: 8),
                _buildTimeRangeSelector(theme, chartIndigo),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: _buildLineChart(theme, chartIndigo),
          ),
        ],
      ),
    );
  }

  Widget _buildTimeRangeSelector(ThemeData theme, [Color? customColor]) {
    final Color primaryColor = customColor ?? theme.colorScheme.primary;

    // Create a map for shorter display names
    final Map<String, String> timeRangeDisplayNames = {
      'Today': 'Today',
      'Last 7 Days': '7D',
      'Last 30 Days': '30D',
      'Last 90 Days': '90D',
      'Last 6 Months': '6M',
      'Last Year': '1Y',
      'All Time': 'All',
    };

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: primaryColor.withAlpha(20),
        borderRadius: BorderRadius.circular(8),
      ),
      child: DropdownButton<String>(
        value: _selectedTimeRange,
        underline: Container(), // Remove underline
        icon: const Icon(Icons.arrow_drop_down, size: 18),
        borderRadius: BorderRadius.circular(8),
        dropdownColor: theme.colorScheme.surface,
        isDense: true, // Make dropdown more compact
        onChanged: (String? newValue) {
          if (newValue != null) {
            setState(() {
              _selectedTimeRange = newValue;
              _invalidateCache(); // Clear all caches when time range changes
            });
          }
        },
        items: <String>[
          'Today',
          'Last 7 Days',
          'Last 30 Days',
          'Last 90 Days',
          'Last 6 Months',
          'Last Year',
          'All Time'
        ].map<DropdownMenuItem<String>>((String value) {
          return DropdownMenuItem<String>(
            value: value,
            child: Text(
              timeRangeDisplayNames[value] ?? value,
              style: TextStyle(
                color: primaryColor,
                fontWeight: FontWeight.w500,
                fontSize: 12, // Smaller font size
              ),
            ),
          );
        }).toList(),
      ),
    );
  }

  // --- Chart Logic ---

  Widget _buildLineChart(ThemeData theme, [Color? customColor]) {
    // Use the provided color or default to primary theme color
    final Color chartColor = customColor ?? Theme.of(context).colorScheme.primary;
    const Color secondaryColor = Color(0xFF00BCD4); // Cyan as secondary color

    // Check if data needs recalculation
    if (_spots == null ||
        _selectedTimeRange != _lastTimeRange ||
        _shouldRecalculateCache()) {
      _spots = _getFilteredSpots();
      _lastTimeRange = _selectedTimeRange;
      _lastUpdateTime = DateTime.now();
    }

    if (_spots == null || _spots!.isEmpty) {
      return const ChartEmptyState(
        icon: Icons.show_chart,
        message: 'No Data Available',
        subtitle: 'No milk records found for the selected time range',
        color: Color(0xFF2E7D32), // Green for summary
        height: 250,
      );
    }

    // Find min/max X (time) and Y (production) for axis scaling
    double minX = _spots!.map((s) => s.x).reduce(math.min);
    double maxX = _spots!.map((s) => s.x).reduce(math.max);
    double minY = _spots!.map((s) => s.y).reduce(math.min);
    double maxY = _spots!.map((s) => s.y).reduce(math.max);

    // Add padding to Y axis
    double yPadding = (maxY - minY) * 0.1; // 10% padding
    minY = math.max(0, minY - yPadding); // Ensure minY is not negative
    maxY += yPadding;
    if (maxY == minY) maxY += 1; // Ensure max > min if only one value

    // Ensure minX and maxX are different for valid range
    if (minX == maxX) {
      maxX += 1; // Add a small amount if only one data point
    }

    return SizedBox(
      height: 250,
      child: LineChart(
        LineChartData(
          minY: minY,
          maxY: maxY,
          minX: minX,
          maxX: maxX,
          gridData: FlGridData(
            show: true,
            drawVerticalLine: true,
            horizontalInterval: (maxY - minY).abs() > 1
                ? (maxY - minY) / 4
                : 1, // Adjust grid lines, avoid div by zero
            verticalInterval: (maxX - minX).abs() > 1 ? (maxX - minX) / 5 : 1,
            getDrawingHorizontalLine: (value) {
              return FlLine(
                color: theme.dividerColor.withAlpha(40),
                strokeWidth: 1,
              );
            },
            getDrawingVerticalLine: (value) {
              return FlLine(
                color: theme.dividerColor.withAlpha(40),
                strokeWidth: 1,
              );
            },
          ),
          titlesData: FlTitlesData(
            show: true,
            rightTitles:
                const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            topTitles:
                const AxisTitles(sideTitles: SideTitles(showTitles: false)),
            bottomTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 30,
                interval: (maxX - minX).abs() > 1 ? (maxX - minX) / 5 : 1,
                getTitlesWidget: (value, meta) =>
                    _bottomTitleWidgets(value, meta, minX, maxX, theme),
              ),
            ),
            leftTitles: AxisTitles(
              sideTitles: SideTitles(
                showTitles: true,
                reservedSize: 40,
                interval: (maxY - minY).abs() > 1 ? (maxY - minY) / 4 : 1,
                getTitlesWidget: (value, meta) =>
                    _leftTitleWidgets(value, meta, theme),
              ),
            ),
          ),
          borderData: FlBorderData(
            show: true,
            border: Border.all(color: theme.dividerColor.withAlpha(60)),
          ),
          lineBarsData: [
            LineChartBarData(
              spots: _spots!,
              isCurved: true,
              gradient: LinearGradient(
                colors: [chartColor, secondaryColor],
              ),
              barWidth: 4,
              isStrokeCapRound: true,
              dotData: FlDotData(
                show: true,
                getDotPainter: (spot, percent, barData, index) =>
                    FlDotCirclePainter(
                  radius: 5,
                  color: theme.colorScheme.surface,
                  strokeWidth: 2,
                  strokeColor: chartColor,
                ),
              ),
              belowBarData: BarAreaData(
                show: true,
                gradient: LinearGradient(
                  colors: [
                    chartColor.withAlpha(80),
                    secondaryColor.withAlpha(20),
                  ],
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                ),
              ),
            ),
          ],
          lineTouchData: LineTouchData(
            touchTooltipData: LineTouchTooltipData(
                getTooltipColor: (LineBarSpot touchedSpot) =>
                    Theme.of(context).colorScheme.surface,
                tooltipRoundedRadius: 8,
                tooltipPadding: const EdgeInsets.all(12),
                tooltipBorder: BorderSide(
                  color: chartColor.withAlpha(100),
                  width: 1,
                ),
                getTooltipItems: (List<LineBarSpot> touchedBarSpots) {
                  return touchedBarSpots.map((barSpot) {
                    final date =
                        DateTime.fromMillisecondsSinceEpoch(barSpot.x.toInt());
                    return LineTooltipItem(
                      '${DateFormat('MMMM dd, yyyy').format(date)}\n',
                      TextStyle(
                          color: theme.colorScheme.onSurface,
                          fontWeight: FontWeight.bold,
                          fontSize: 14),
                      children: <TextSpan>[
                        TextSpan(
                          text: '${barSpot.y.toStringAsFixed(1)} L',
                          style: TextStyle(
                              color: chartColor,
                              fontWeight: FontWeight.w900,
                              fontSize: 16),
                        ),
                      ],
                    );
                  }).toList();
                }),
            handleBuiltInTouches: true,
          ),
        ),
      ),
    );
  }

  // Check if cache should be recalculated (e.g., data updated recently)
  bool _shouldRecalculateCache() {
    if (_lastUpdateTime == null) return true;
    // Recalculate if data changed in the last minute (adjust as needed)
    return DateTime.now().difference(_lastUpdateTime!).inSeconds > 60;
  }

  List<FlSpot> _getFilteredSpots() {
    // Use the optimized filtered records method
    final filteredRecords = _getFilteredRecords();

    if (filteredRecords.isEmpty) {
      return [];
    }

    // Group records by day and sum total production
    Map<String, double> dailyData = {};
    final DateFormat formatter = DateFormat('yyyy-MM-dd');

    for (var record in filteredRecords) {
      if (record.date != null) {
        String dayKey = formatter.format(record.date!);
        // Use totalAmount which should be calculated correctly now
        dailyData.update(dayKey, (value) => value + (record.totalAmount ?? 0),
            ifAbsent: () => record.totalAmount ?? 0);
      }
    }

    // Convert daily data to FlSpot list with improved spacing
    List<FlSpot> spots = [];
    final sortedDays = dailyData.keys.toList()..sort();

    for (int i = 0; i < sortedDays.length; i++) {
      final dayString = sortedDays[i];
      final total = dailyData[dayString]!;
      final day = DateTime.parse(dayString);

      // Use actual timestamp for better chart accuracy
      spots.add(FlSpot(day.millisecondsSinceEpoch.toDouble(), total));
    }

    return spots;
  }

  // Helper to calculate date from chart x-value
  DateTime calculateDateFromValue(double value) {
    return DateTime.fromMillisecondsSinceEpoch(value.toInt());
  }

  Widget _bottomTitleWidgets(
      double value, TitleMeta meta, double minX, double maxX, ThemeData theme) {
    final DateTime date = calculateDateFromValue(value);
    final String text = DateFormat('d MMM').format(date);
    final bool showLabel = value % 2 == 0;

    if (showLabel) {
      return SideTitleWidget(
        space: 4,
        meta: meta,
        child: Text(
          text,
          style: TextStyle(
            fontSize: 10,
            color: theme.colorScheme.onSurfaceVariant,
          ),
        ),
      );
    } else {
      return Container();
    }
  }

  Widget _leftTitleWidgets(double value, TitleMeta meta, ThemeData theme) {
    final text = value.toInt().toString();

    return SideTitleWidget(
      space: 4,
      meta: meta,
      child: Text(
        text,
        style: TextStyle(
          fontSize: 10,
          color: theme.colorScheme.onSurfaceVariant,
        ),
      ),
    );
  }

  // --- Records Tab Components ---

  Widget _buildMilkRecordItem(MilkRecordIsar record, ThemeData theme) {
    final recordDate = record.date != null
        ? DateFormat('MMMM dd, yyyy').format(record.date!)
        : 'No Date';

    const Color primaryGreen = Color(0xFF2E7D32);
    const Color morningBlue = Colors.blue;
    const Color eveningPurple = Colors.purple;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: primaryGreen.withAlpha(26),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: primaryGreen.withAlpha(50),
                        child: const Icon(
                          Icons.opacity,
                          color: primaryGreen,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              recordDate,
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'Total: ${record.totalAmount?.toStringAsFixed(1) ?? '0.0'} L',
                              style: const TextStyle(
                                fontSize: 15,
                                fontWeight: FontWeight.w600,
                                color: primaryGreen,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert),
                  tooltip: 'More options',
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('Edit'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(
                            Icons.delete,
                            color: Colors.red,
                          ),
                          SizedBox(width: 8),
                          Text(
                            'Delete',
                            style: TextStyle(
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _showEditRecordDialog(record);
                        break;
                      case 'delete':
                        _confirmDeleteRecord(record);
                        break;
                    }
                  },
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                _buildMilkAmountRow(
                  Icons.wb_sunny_outlined,
                  'Morning',
                  record.morningAmount?.toStringAsFixed(1) ?? '-',
                  morningBlue,
                  theme,
                ),
                const SizedBox(height: 8),
                _buildMilkAmountRow(
                  Icons.nightlight_outlined,
                  'Evening',
                  record.eveningAmount?.toStringAsFixed(1) ?? '-',
                  eveningPurple,
                  theme,
                ),
                if (record.notes != null && record.notes!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  _buildMilkAmountRow(
                    Icons.notes_outlined,
                    'Notes',
                    record.notes!,
                    const Color(0xFF3F51B5), // Indigo instead of grey
                    theme,
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMilkAmountRow(
      IconData icon, String label, String value, Color color, ThemeData theme) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withAlpha(20),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, size: 20, color: color),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: theme.colorScheme.onSurface.withAlpha(204),
            ),
          ),
        ),
        Text(
          '$value L',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: theme.colorScheme.onSurface,
          ),
        ),
      ],
    );
  }

  // --- Dialogs and Actions ---

  Future<void> _showAddRecordDialog() async {
    final businessId = widget.cattle.businessId;
    if (businessId == null || businessId.isEmpty) {
      CattleMessageUtils.showError(context, 'Cannot add record: Cattle ID missing.');
      return;
    }

    // Now expecting MilkRecordIsar result from dialog
    final newRecord = await showDialog<MilkRecordIsar>(
      context: context,
      builder: (context) => MilkFormDialog(
        // Pass cattleTagId if needed by dialog, ensure it's correct
        cattleTagId: widget.cattle.tagId,
      ),
    );

    if (!_isMounted || newRecord == null) return;

    // Use local state update instead of reloading all records
    _safeSetState(() {
      // Add the new record at the beginning of the list (most recent first)
      _records.insert(0, newRecord);
      // Recalculate summary and status
      _calculateSummary();
      _updateStatus();
      // Invalidate chart cache
      _spots = null;
    });

    if (mounted) {
      CattleMessageUtils.showSuccess(context, 'Milk record added successfully');
    }
  }

  Future<void> _showEditRecordDialog(MilkRecordIsar record) async {
    // Now expecting MilkRecordIsar result
    final updatedRecord = await showDialog<MilkRecordIsar>(
      context: context,
      builder: (context) => MilkFormDialog(
        record: record,
        cattleTagId: record.cattleTagId, // Pass existing record data
      ),
    );

    if (!_isMounted || updatedRecord == null) return;

    // Use local state update instead of reloading all records
    _safeSetState(() {
      // Find and replace the record in the list
      final index = _records.indexWhere((r) => r.id == updatedRecord.id);
      if (index != -1) {
        _records[index] = updatedRecord;
      } else {
        // If not found (unlikely), add it to the beginning
        _records.insert(0, updatedRecord);
      }
      // Re-sort records to maintain order
      _records.sort((a, b) => (b.date ?? DateTime(0)).compareTo(a.date ?? DateTime(0)));
      // Recalculate summary and status
      _calculateSummary();
      _updateStatus();
      // Invalidate chart cache
      _spots = null;
    });

    if (mounted) {
      CattleMessageUtils.showSuccess(context, 'Milk record updated successfully');
    }
  }

  Future<void> _confirmDeleteRecord(MilkRecordIsar record) async {
    final recordIdToDelete = record.id; // Isar ID is int

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Confirm Deletion'),
          content: Text(
              'Are you sure you want to delete the milk record from ${record.date != null ? DateFormat('dd MMM yyyy').format(record.date!) : 'this date'}?'), // Handle null date
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            TextButton(
              style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error),
              child: const Text('Delete'),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    );

    if (!_isMounted || confirmed != true) return;

    try {
      // Call handler with the correct int ID
      await _milkHandler.deleteMilkRecord(recordIdToDelete);

      if (!_isMounted) return;

      // Local state update for delete
      _safeSetState(() {
        _records.removeWhere((r) => r.id == recordIdToDelete);
        _calculateSummary();
        _updateStatus();
        _spots = null; // Invalidate chart cache
      });
      if (mounted) {
        CattleMessageUtils.showSuccess(context, 'Milk record deleted successfully.');
      }
    } catch (e) {
      if (_isMounted && mounted) {
        CattleMessageUtils.showError(context, _getReadableErrorMessage(e));
      }
    }
    // No _loadData() call after successful delete with local update
  }




}
