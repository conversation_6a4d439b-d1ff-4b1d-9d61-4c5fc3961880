import 'package:flutter/material.dart';
import '../Dashboard/Cattle/screens/cattle_detail_screen.dart';
import 'package:logging/logging.dart';
import 'package:get_it/get_it.dart';
import '../Dashboard/Cattle/services/cattle_handler.dart';
import '../Dashboard/Farm Setup/services/farm_setup_handler.dart';
import '../Dashboard/Farm Setup/models/animal_type_isar.dart';
import 'message_utils.dart';

class SmoothPageRoute<T> extends MaterialPageRoute<T> {
  SmoothPageRoute({
    required WidgetBuilder builder,
    RouteSettings? settings,
  }) : super(builder: builder, settings: settings);

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return SlideTransition(
      position: Tween<Offset>(
        begin: const Offset(1.0, 0.0),
        end: Offset.zero,
      ).animate(
        CurvedAnimation(
          parent: animation,
          curve: Curves.easeInOut,
        ),
      ),
      child: child,
    );
  }
}

Future<void> navigateToCattleDetails(
  BuildContext context,
  String tagId,
) async {
  final logger = Logger('NavigationUtils');

  try {
    // Get handlers using GetIt
    final cattleHandler = GetIt.instance<CattleHandler>();
    final farmSetupHandler = GetIt.instance<FarmSetupHandler>();

    // Get the cattle details
    final cattle = await cattleHandler.getCattleByTagId(tagId);

    // Check if the context is still valid
    if (!context.mounted) return;

    if (cattle == null) {
      MessageUtils.showError(context, 'Cattle not found');
      return;
    }

    // Get the breed data - we're not using this but keeping the code in case we need it later
    /*
    BreedCategoryIsar? breed;
    if (cattle.breedId != null) {
      final breeds = await farmSetupHandler.getAllBreedCategories();
      breed = breeds.firstWhere(
        (b) => b.businessId == cattle.breedId,
        orElse: () => BreedCategoryIsar(),
      );
    }
    */

    // Get the animal type data
    AnimalTypeIsar? animalType;
    if (cattle.animalTypeId != null) {
      final animalTypes = await farmSetupHandler.getAllAnimalTypes();
      animalType = animalTypes.firstWhere(
        (t) => t.businessId == cattle.animalTypeId,
        orElse: () => AnimalTypeIsar(),
      );
    }

    if (animalType == null) {
      if (context.mounted) {
        MessageUtils.showError(context, 'Animal type not found');
      }
      return;
    }

    if (context.mounted) {
      Navigator.push(
        context,
        SmoothPageRoute(
          builder: (context) => CattleDetailScreen(
            existingCattle: cattle,
            businessId: cattle.businessId ?? '',
            onCattleUpdated: (updatedCattle) async {
              try {
                // Update the database using the cattle handler
                await cattleHandler.updateCattle(updatedCattle);

                // Optionally refresh the previous screen's data when navigating back
                if (context.mounted) {
                  Navigator.of(context).pop();
                }
              } catch (e) {
                logger.severe('Error updating cattle: $e');
                if (context.mounted) {
                  MessageUtils.showError(context, 'Failed to update cattle: $e');
                }
              }
            },
          ),
        ),
      );
    }
  } catch (e) {
    logger.severe('Error navigating to cattle details: $e');
    if (context.mounted) {
      MessageUtils.showError(context, 'An error occurred: $e');
    }
  }
}
