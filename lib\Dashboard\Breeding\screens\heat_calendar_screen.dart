import 'package:flutter/material.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:get_it/get_it.dart';
import 'dart:async';
import '../../../constants/app_colors.dart';
import '../models/breeding_event_isar.dart';
import '../models/breeding_record_isar.dart';
import '../services/breeding_handler.dart';
import '../dialogs/breeding_form_dialog.dart';
import '../../../services/database/database_helper.dart';

class HeatCalendarScreen extends StatefulWidget {
  const HeatCalendarScreen({super.key});

  @override
  State<HeatCalendarScreen> createState() => _HeatCalendarScreenState();
}

class _HeatCalendarScreenState extends State<HeatCalendarScreen> {
  late final BreedingHandler _breedingHandler;
  late CalendarFormat _calendarFormat;
  late DateTime _focusedDay;
  late DateTime _selectedDay;
  late Map<DateTime, List<BreedingEventIsar>> _events;
  late ValueNotifier<List<BreedingEventIsar>> _selectedEvents;
  bool _isLoading = true;

  // Stream subscription
  StreamSubscription<Map<String, dynamic>>? _breedingStreamSubscription;

  @override
  void initState() {
    super.initState();
    _breedingHandler = GetIt.instance<BreedingHandler>();
    _calendarFormat = CalendarFormat.month;
    _focusedDay = DateTime.now();
    _selectedDay = DateTime.now();
    _events = {};
    _selectedEvents = ValueNotifier([]);
    _loadEvents();
    _subscribeToRecordUpdates();
  }

  // Subscribe to breeding record updates
  void _subscribeToRecordUpdates() {
    final databaseHelper = GetIt.instance<DatabaseHelper>();
    _breedingStreamSubscription =
        databaseHelper.breedingRecordStream.listen(_handleRecordUpdate);
  }

  // Handle updates from the stream
  void _handleRecordUpdate(Map<String, dynamic> event) {
    final action = event['action'] as String?;

    if (action == 'add' || action == 'update' || action == 'delete') {
      // Instead of reloading all events, we'll update the local state
      // For simplicity in this implementation, we'll refresh the data
      // In a full implementation, we would update the specific records in _events
      _loadEvents();
    }
  }

  void _loadEvents() async {
    if (!mounted) return;

    setState(() => _isLoading = true);

    try {
      // Get all breeding events using the handler
      final breedingEvents = await _breedingHandler.getAllBreedingEvents();

      // Create map of events by date
      final events = <DateTime, List<BreedingEventIsar>>{};

      // Process breeding events
      for (final event in breedingEvents) {
        if (event.date != null) {
          final eventDate = event.date!;
          final key = DateTime(eventDate.year, eventDate.month, eventDate.day);

          if (events[key] == null) {
            events[key] = [];
          }

          events[key]!.add(event);
        }
      }

      if (mounted) {
        setState(() {
          _events = events;
          _selectedEvents.value = _getEventsForDay(_selectedDay);
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() => _isLoading = false);
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_getReadableErrorMessage(e)),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  // Add a new breeding event to the local state (efficient update)

  // Update a breeding event in the local state (efficient update)

  // Remove a breeding event from the local state (efficient update)

  // Get user-friendly error message
  String _getReadableErrorMessage(dynamic error) {
    if (error is Exception) {
      final message = error.toString().replaceAll('Exception: ', '');
      return message.isNotEmpty
          ? message
          : 'An error occurred while loading events';
    }
    return 'Error loading calendar events: $error';
  }

  List<BreedingEventIsar> _getEventsForDay(DateTime day) {
    final normalizedDay = DateTime(day.year, day.month, day.day);
    return _events[normalizedDay] ?? [];
  }

  @override
  void dispose() {
    _selectedEvents.dispose();
    _breedingStreamSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final primaryColor = theme.primaryColor;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Heat Calendar'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadEvents,
            tooltip: 'Refresh Calendar',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : Column(
              children: [
                TableCalendar(
                  firstDay: DateTime.utc(2020, 1, 1),
                  lastDay: DateTime.utc(2030, 12, 31),
                  focusedDay: _focusedDay,
                  calendarFormat: _calendarFormat,
                  eventLoader: _getEventsForDay,
                  selectedDayPredicate: (day) {
                    return isSameDay(_selectedDay, day);
                  },
                  onDaySelected: (selectedDay, focusedDay) {
                    if (!isSameDay(_selectedDay, selectedDay)) {
                      setState(() {
                        _selectedDay = selectedDay;
                        _focusedDay = focusedDay;
                        _selectedEvents.value = _getEventsForDay(selectedDay);
                      });
                    }
                  },
                  onFormatChanged: (format) {
                    if (_calendarFormat != format) {
                      setState(() {
                        _calendarFormat = format;
                      });
                    }
                  },
                  onPageChanged: (focusedDay) {
                    _focusedDay = focusedDay;
                  },
                  calendarStyle: CalendarStyle(
                    markerDecoration: const BoxDecoration(
                      color: Colors.blue,
                      shape: BoxShape.circle,
                    ),
                    todayDecoration: BoxDecoration(
                      color: primaryColor.withAlpha(76),
                      shape: BoxShape.circle,
                    ),
                    selectedDecoration: BoxDecoration(
                      color: primaryColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  headerStyle: HeaderStyle(
                    formatButtonTextStyle: TextStyle(color: primaryColor),
                    titleTextStyle: TextStyle(
                      color: primaryColor,
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: Wrap(
                    spacing: 16,
                    runSpacing: 8,
                    children: [
                      _ColorLegendItem(
                          color: theme.colorScheme.error, label: 'Heat'),
                      _ColorLegendItem(
                          color: theme.colorScheme.primary, label: 'Breeding'),
                      _ColorLegendItem(
                          color: theme.colorScheme.tertiary,
                          label: 'Expected Heat'),
                      _ColorLegendItem(
                          color: theme.colorScheme.secondary,
                          label: 'Expected Calving'),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Expanded(
                  child: ValueListenableBuilder<List<BreedingEventIsar>>(
                    valueListenable: _selectedEvents,
                    builder: (context, events, _) {
                      return events.isEmpty
                          ? const Center(
                              child: Text('No breeding events for this day'),
                            )
                          : ListView.builder(
                              itemCount: events.length,
                              itemBuilder: (context, index) {
                                final event = events[index];
                                return Card(
                                  margin: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 4,
                                  ),
                                  child: ListTile(
                                    leading: CircleAvatar(
                                      backgroundColor: _getEventColor(
                                          event.eventType ?? '', theme),
                                      child: Icon(
                                        _getEventIcon(event.eventType ?? ''),
                                        color: Colors.white,
                                      ),
                                    ),
                                    title: Row(
                                      children: [
                                        Expanded(
                                          child: Text(
                                            '${event.cattleName ?? 'Unknown Cattle'} - ${event.eventType ?? 'Event'}',
                                            style: const TextStyle(
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 2,
                                          ),
                                          decoration: BoxDecoration(
                                            color: _getStatusColor(
                                                event.status ?? 'Pending',
                                                theme),
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          child: Text(
                                            event.status ?? 'Pending',
                                            style: const TextStyle(
                                              color: Colors.white,
                                              fontSize: 12,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                    subtitle: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        const SizedBox(height: 4),
                                        Text(event.details ?? ''),
                                        if (event.relatedRecordId != null)
                                          Align(
                                            alignment: Alignment.centerRight,
                                            child: TextButton(
                                              onPressed: () =>
                                                  _viewRelatedRecord(
                                                      event.relatedRecordId!),
                                              child: const Text('View Record'),
                                            ),
                                          ),
                                      ],
                                    ),
                                    isThreeLine: true,
                                  ),
                                );
                              },
                            );
                    },
                  ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        backgroundColor: AppColors.primary,
        onPressed: () => _addBreedingRecord(context),
        child: const Icon(Icons.add),
      ),
    );
  }

  void _addBreedingRecord(BuildContext context) async {
    // Store context-dependent objects before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final themeData = Theme.of(context);
    final primaryColor = themeData.primaryColor;
    final errorColor = themeData.colorScheme.error;

    final record = await showDialog<BreedingRecordIsar>(
      context: context,
      builder: (context) => const BreedingFormDialog(),
    );

    if (record != null && mounted) {
      try {
        // Add the breeding record using the handler
        await _breedingHandler.addBreedingRecord(record);

        // Instead of reloading all events, we would ideally:
        // 1. Check what events would be generated from this breeding record
        // 2. Add them directly to our local state
        // 3. Update the UI

        // For now, we'll reload all events since the event generation logic
        // is not directly accessible here
        _loadEvents();

        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: const Text('Breeding record added successfully'),
              backgroundColor: primaryColor,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: Text(_getReadableErrorMessage(e)),
              backgroundColor: errorColor,
            ),
          );
        }
      }
    }
  }

  Future<void> _viewRelatedRecord(String recordId) async {
    if (!mounted) return;

    // Store context-dependent objects before async gap
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final themeData = Theme.of(context);
    final errorColor = themeData.colorScheme.error;

    try {
      // Get the breeding record by ID
      final record = await _breedingHandler.getBreedingRecordById(recordId);

      if (record == null) {
        if (mounted) {
          scaffoldMessenger.showSnackBar(
            SnackBar(
              content: const Text('Record not found'),
              backgroundColor: errorColor,
            ),
          );
        }
        return;
      }

      // Show the edit dialog
      if (!mounted) return;

      final updatedRecord = await showDialog<BreedingRecordIsar>(
        context: context,
        builder: (context) => BreedingFormDialog(record: record),
      );

      if (updatedRecord != null && mounted) {
        // Update the record
        await _breedingHandler.updateBreedingRecord(updatedRecord);

        // Local state update (ideally we would update just the affected events)
        _loadEvents();
      }
    } catch (e) {
      if (mounted) {
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text(_getReadableErrorMessage(e)),
            backgroundColor: errorColor,
          ),
        );
      }
    }
  }

  Color _getEventColor(String eventType, ThemeData theme) {
    switch (eventType.toLowerCase()) {
      case 'heat':
        return theme.colorScheme.error;
      case 'breeding':
        return theme.colorScheme.primary;
      case 'expected heat':
        return theme.colorScheme.tertiary;
      case 'expected calving':
        return theme.colorScheme.secondary;
      default:
        return Colors.grey;
    }
  }

  IconData _getEventIcon(String eventType) {
    switch (eventType.toLowerCase()) {
      case 'heat':
        return Icons.whatshot;
      case 'breeding':
        return Icons.favorite;
      case 'expected heat':
        return Icons.whatshot_outlined;
      case 'expected calving':
        return Icons.child_care;
      default:
        return Icons.event;
    }
  }

  Color _getStatusColor(String status, ThemeData theme) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'completed':
        return theme.colorScheme.primary;
      case 'confirmed':
        return theme.colorScheme.secondary;
      case 'cancelled':
      case 'failed':
        return theme.colorScheme.error;
      default:
        return const Color(0xFF795548); // Brown - for unknown status
    }
  }
}

class _ColorLegendItem extends StatelessWidget {
  final Color color;
  final String label;

  const _ColorLegendItem({
    required this.color,
    required this.label,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          width: 16,
          height: 16,
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
          ),
        ),
        const SizedBox(width: 4),
        Text(label, style: const TextStyle(fontSize: 12)),
      ],
    );
  }
}
