import 'package:flutter/material.dart';
import '../models/milk_record_isar.dart';
import '../services/milk_service.dart';
import '../../Cattle/models/cattle_isar.dart';
import '../../Farm Setup/models/animal_type_isar.dart';
import '../../Cattle/services/cattle_handler.dart';
import '../../Farm Setup/services/farm_setup_handler.dart';
import '../tabs/daily_tab.dart';
import '../tabs/weekly_tab.dart';
import '../tabs/monthly_tab.dart';
import 'package:intl/intl.dart';
import 'dart:async';
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import '../../../utils/message_utils.dart';

class MilkSummaryScreen extends StatefulWidget {
  const MilkSummaryScreen({super.key});

  @override
  State<MilkSummaryScreen> createState() =>
      _MilkSummaryScreenState();
}

class _MilkSummaryScreenState extends State<MilkSummaryScreen>
    with SingleTickerProviderStateMixin {
  final MilkService _milkService = MilkService();
  final CattleHandler _cattleHandler = CattleHandler.instance;
  final FarmSetupHandler _farmSetupHandler = FarmSetupHandler.instance;
  late TabController _tabController;
  bool _isLoading = true;
  bool _isMounted = true;
  List<MilkRecordIsar> _records = [];
  Map<String, CattleIsar> _cattleMap = {};
  Map<String, AnimalTypeIsar> _animalTypeMap = {};
  String _selectedAnimalType = 'All';
  String _selectedTagId = 'All';
  List<AnimalTypeIsar> _animalTypes = [];
  List<String> _tagIds = [];
  DateTime? _selectedDate;
  DateTime? _selectedWeek;
  DateTime? _selectedMonth;
  final currencyFormat = NumberFormat.currency(locale: 'en_US', symbol: '\$');

  // Performance optimization: Caching
  List<MilkRecordIsar>? _cachedFilteredRecords;
  String? _lastFilterKey;
  Timer? _debounceTimer;

  // Error handling
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _tabController.addListener(() {
      // Force rebuild when tab changes to update icon colors
      if (mounted) setState(() {});
    });
    _initializeData();
  }

  @override
  void dispose() {
    _isMounted = false;
    _debounceTimer?.cancel();
    _tabController.dispose();
    super.dispose();
  }

  // Safely update state only if widget is mounted
  void _safeSetState(VoidCallback fn) {
    if (_isMounted && mounted) {
      setState(fn);
    }
  }

  // Generate cache key for filtering
  String _generateFilterKey() {
    return '${_selectedAnimalType}_$_selectedTagId';
  }

  // Invalidate cache when filters change
  void _invalidateFilterCache() {
    _cachedFilteredRecords = null;
    _lastFilterKey = null;
  }

  Future<void> _initializeData() async {
    try {
      _safeSetState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Load all required data concurrently for better performance
      final futures = await Future.wait([
        _farmSetupHandler.getAllAnimalTypes(),
        _cattleHandler.getAllCattle(),
        _milkService.getMilkRecords(),
      ]);

      final animalTypes = futures[0] as List<AnimalTypeIsar>;
      final allCattle = futures[1] as List<CattleIsar>;
      final records = futures[2] as List<MilkRecordIsar>;

      // Create maps for efficient lookups
      final cattleMap = {for (var cattle in allCattle) cattle.tagId ?? '': cattle};
      final animalTypeMap = {for (var type in animalTypes) type.businessId ?? '': type};

      // Get unique tag IDs for female cattle only
      final tagIds = allCattle
          .where((c) => c.gender?.toLowerCase() == 'female')
          .map((c) => c.tagId ?? '')
          .where((id) => id.isNotEmpty)
          .toList()
        ..sort();

      _safeSetState(() {
        _cattleMap = cattleMap;
        _animalTypeMap = animalTypeMap;
        _animalTypes = animalTypes;
        _tagIds = tagIds;
        _records = records;
        _isLoading = false;
        _invalidateFilterCache(); // Clear cache when data changes
      });
    } catch (e) {
      _safeSetState(() {
        _isLoading = false;
        _errorMessage = _getReadableErrorMessage(e);
      });

      if (mounted) {
        MilkMessageUtils.showError(context, _errorMessage ?? 'Error loading data');
      }
    }
  }

  // Enhanced error message handler
  String _getReadableErrorMessage(dynamic e) {
    final errorMsg = e.toString().toLowerCase();

    if (errorMsg.contains('permission')) {
      return 'Permission denied. Please check app permissions.';
    } else if (errorMsg.contains('database')) {
      return 'Database error. Please restart the app and try again.';
    } else if (errorMsg.contains('network') || errorMsg.contains('connection')) {
      return 'Network error. Please check your connection.';
    } else if (errorMsg.contains('timeout')) {
      return 'Operation timed out. Please try again.';
    } else if (errorMsg.contains('not found')) {
      return 'Data not found. Please refresh and try again.';
    }

    return 'An unexpected error occurred. Please try again.';
  }

  // Export functionality
  Future<void> _exportData() async {
    try {
      final records = filteredRecords;
      if (records.isEmpty) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No data to export'),
              backgroundColor: Colors.orange,
              behavior: SnackBarBehavior.floating,
              margin: EdgeInsets.all(16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
            ),
          );
        }
        return;
      }

      // Generate CSV content
      final csvContent = _generateCSVContent(records);

      // Get temporary directory
      final directory = await getTemporaryDirectory();
      final timestamp = DateFormat('yyyyMMdd_HHmmss').format(DateTime.now());
      final fileName = 'milk_summary_$timestamp.csv';
      final file = File('${directory.path}/$fileName');

      // Write CSV content to file
      await file.writeAsString(csvContent);

      // Share the file
      await Share.shareXFiles(
        [XFile(file.path)],
        text: 'Milk Summary Export',
        subject: 'Milk Summary - $timestamp',
      );

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Data exported successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Export failed: ${_getReadableErrorMessage(e)}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  String _generateCSVContent(List<MilkRecordIsar> records) {
    final buffer = StringBuffer();

    // CSV Header
    buffer.writeln('Date,Cattle Tag ID,Cattle Name,Animal Type,Morning Amount (L),Evening Amount (L),Total Amount (L)');

    // CSV Data
    for (var record in records) {
      final cattle = _cattleMap[record.cattleTagId];
      final animalType = cattle != null ? _animalTypeMap[cattle.animalTypeId] : null;

      final date = record.date != null ? DateFormat('yyyy-MM-dd').format(record.date!) : '';
      final tagId = record.cattleTagId ?? '';
      final cattleName = cattle?.name ?? 'Unknown';
      final animalTypeName = animalType?.name ?? 'Unknown';
      final morningAmount = (record.morningAmount ?? 0).toStringAsFixed(2);
      final eveningAmount = (record.eveningAmount ?? 0).toStringAsFixed(2);
      final totalAmount = record.totalYield.toStringAsFixed(2);

      buffer.writeln('$date,$tagId,"$cattleName","$animalTypeName",$morningAmount,$eveningAmount,$totalAmount');
    }

    return buffer.toString();
  }

  // Refresh data functionality
  Future<void> _refreshData() async {
    await _initializeData();
  }



  // Error state widget
  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Colors.red.shade400,
            ),
            const SizedBox(height: 16),
            Text(
              'Error Loading Data',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: Colors.red.shade700,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              _errorMessage ?? 'An unexpected error occurred',
              textAlign: TextAlign.center,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey.shade600,
              ),
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _refreshData,
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
              style: ElevatedButton.styleFrom(
                backgroundColor: _primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  List<MilkRecordIsar> get filteredRecords {
    final currentFilterKey = _generateFilterKey();

    // Use cached results if available and filters haven't changed
    if (_cachedFilteredRecords != null && _lastFilterKey == currentFilterKey) {
      return _cachedFilteredRecords!;
    }

    // Apply filters and cache results
    final filtered = _records.where((record) {
      final cattle = _cattleMap[record.cattleTagId];
      if (cattle == null) return false;

      // Animal type filter
      final matchesAnimalType = _selectedAnimalType == 'All' ||
          (_animalTypeMap[cattle.animalTypeId]?.name ?? '') ==
              _selectedAnimalType;

      // Tag ID filter
      final matchesTagId =
          _selectedTagId == 'All' || record.cattleTagId == _selectedTagId;

      return matchesAnimalType && matchesTagId;
    }).toList();

    // Cache the results
    _cachedFilteredRecords = filtered;
    _lastFilterKey = currentFilterKey;

    return filtered;
  }

  Widget _buildFilterSection() {
    // Use the current tab color for filter styling
    final Color filterColor = _getIndicatorColor();

    return Container(
      color: Colors.white,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              // Animal Type Filter
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                        color: filterColor.withValues(alpha: 0.3)),
                  ),
                  child: PopupMenuButton<String>(
                    position: PopupMenuPosition.under,
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'All',
                        child: Text(
                          'All Types',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                        ),
                      ),
                      ..._animalTypes.map((type) => PopupMenuItem(
                            value: type.name,
                            child: Text(
                              type.name ?? 'Unknown',
                              style: const TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 15,
                              ),
                            ),
                          )),
                    ],
                    onSelected: (value) {
                      _debounceTimer?.cancel();
                      _debounceTimer = Timer(const Duration(milliseconds: 300), () {
                        _safeSetState(() {
                          _selectedAnimalType = value;
                          _invalidateFilterCache();
                        });
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Text(
                              _selectedAnimalType,
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 15,
                                color: filterColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Icon(
                            Icons.arrow_drop_down,
                            color: filterColor.withValues(alpha: 0.8),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              // Tag ID Filter
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                        color: filterColor.withValues(alpha: 0.3)),
                  ),
                  child: PopupMenuButton<String>(
                    position: PopupMenuPosition.under,
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'All',
                        child: Text(
                          'All Cattle',
                          style: TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 15,
                          ),
                        ),
                      ),
                      ..._tagIds.map((tagId) {
                        final cattle = _cattleMap[tagId];
                        final name = cattle?.name ?? 'Unknown';
                        return PopupMenuItem(
                          value: tagId,
                          child: Text(
                            '$name ($tagId)',
                            style: const TextStyle(
                              fontWeight: FontWeight.w600,
                              fontSize: 15,
                            ),
                          ),
                        );
                      }),
                    ],
                    onSelected: (value) {
                      _debounceTimer?.cancel();
                      _debounceTimer = Timer(const Duration(milliseconds: 300), () {
                        _safeSetState(() {
                          _selectedTagId = value;
                          _invalidateFilterCache();
                        });
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Flexible(
                            child: Text(
                              _selectedTagId == 'All'
                                  ? 'All Cattle'
                                  : '${_cattleMap[_selectedTagId]?.name ?? 'Unknown'} ($_selectedTagId)',
                              style: TextStyle(
                                fontWeight: FontWeight.w600,
                                fontSize: 15,
                                color: filterColor,
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Icon(
                            Icons.arrow_drop_down,
                            color: filterColor.withValues(alpha: 0.8),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate(BuildContext context) async {
    // Get available dates from records
    final recordsByDate = <DateTime, List<MilkRecordIsar>>{};
    for (var record in filteredRecords) {
      if (record.date != null) {
        final date = DateTime(
          record.date!.year, 
          record.date!.month, 
          record.date!.day
        );
        recordsByDate.putIfAbsent(date, () => []).add(record);
      }
    }

    final availableDates = recordsByDate.keys.toList()
      ..sort((a, b) => b.compareTo(a));

    if (availableDates.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('No records available to select date from')),
        );
      }
      return;
    }

    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _selectedDate ?? availableDates.first,
      firstDate: availableDates.last,
      lastDate: availableDates.first,
      builder: (context, child) {
        return Theme(
          data: Theme.of(context).copyWith(
            colorScheme: ColorScheme.light(
              primary: _getIndicatorColor(), // Use current tab color
            ),
          ),
          child: child!,
        );
      },
    );

    if (picked != null && picked != _selectedDate) {
      _safeSetState(() {
        _selectedDate = picked;
      });
    }
  }

  Future<void> _selectWeek(
      BuildContext context, List<DateTime> availableWeeks) async {
    if (availableWeeks.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('No weekly records available to select from')),
        );
      }
      return;
    }

    // Store the context before async gap
    final currentContext = context;

    // Create a simple dialog to select a week
    final DateTime? picked = await showDialog<DateTime>(
      context: currentContext,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Select Week'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              itemCount: availableWeeks.length,
              itemBuilder: (context, index) {
                final weekStart = availableWeeks[index];
                final weekEnd = weekStart.add(const Duration(days: 6));
                return ListTile(
                  title:
                      Text('Week of ${DateFormat('MMM d').format(weekStart)}'),
                  subtitle: Text(
                      '${DateFormat('MMM d').format(weekStart)} - ${DateFormat('MMM d').format(weekEnd)}'),
                  onTap: () {
                    Navigator.of(dialogContext).pop(weekStart);
                  },
                  selected: _selectedWeek != null &&
                      _selectedWeek!.year == weekStart.year &&
                      _selectedWeek!.month == weekStart.month &&
                      _selectedWeek!.day == weekStart.day,
                  selectedTileColor: _getIndicatorColor().withValues(alpha: 0.9),
                );
              },
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
            ),
          ],
        );
      },
    );

    // Check if widget is still mounted before using setState
    if (picked != null && mounted) {
      _safeSetState(() {
        _selectedWeek = picked;
      });
    }
  }

  Future<void> _selectMonth(
      BuildContext context, List<DateTime> availableMonths) async {
    if (availableMonths.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
              content: Text('No monthly records available to select from')),
        );
      }
      return;
    }

    // Store the context before async gap
    final currentContext = context;

    // Create a simple dialog to select a month
    final DateTime? picked = await showDialog<DateTime>(
      context: currentContext,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          title: const Text('Select Month'),
          content: SizedBox(
            width: double.maxFinite,
            height: 300,
            child: ListView.builder(
              itemCount: availableMonths.length,
              itemBuilder: (context, index) {
                final month = availableMonths[index];
                return ListTile(
                  title: Text(DateFormat('MMMM yyyy').format(month)),
                  onTap: () {
                    Navigator.of(dialogContext).pop(month);
                  },
                  selected: _selectedMonth != null &&
                      _selectedMonth!.year == month.year &&
                      _selectedMonth!.month == month.month,
                  selectedTileColor: _getIndicatorColor().withValues(alpha: 0.9),
                );
              },
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(dialogContext).pop();
              },
            ),
          ],
        );
      },
    );

    // Check if widget is still mounted before using setState
    if (picked != null && mounted) {
      _safeSetState(() {
        _selectedMonth = picked;
      });
    }
  }

  // Color scheme matching milk module
  static const _primaryColor = Color(0xFF2E7D32); // Dark Green
  static const _secondaryColor = Color(0xFF1565C0); // Blue
  static const _accentColor = Color(0xFF6A1B9A); // Purple

  // Get the indicator color based on the selected tab
  Color _getIndicatorColor() {
    switch (_tabController.index) {
      case 0:
        return _primaryColor; // Green for daily
      case 1:
        return _secondaryColor; // Blue for weekly
      case 2:
        return _accentColor; // Purple for monthly
      default:
        return _primaryColor; // Default to green
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Milk Summary',
          style: TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: _primaryColor,
        iconTheme: const IconThemeData(color: Colors.white),
        elevation: 4,
        actions: [
          // Refresh button
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _isLoading ? null : _refreshData,
            tooltip: 'Refresh Data',
          ),
          // Export button
          IconButton(
            icon: const Icon(Icons.file_download, color: Colors.white),
            onPressed: _isLoading ? null : _exportData,
            tooltip: 'Export Data',
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _errorMessage != null
              ? _buildErrorState()
              : Container(
              color: Colors.grey[50], // Light background like milk tab
              child: Column(
                children: [
                  // Tab Bar Section
                  Container(
                    color: Colors.white,
                    child: TabBar(
                      controller: _tabController,
                      labelColor: _getIndicatorColor(),
                      unselectedLabelColor: _getIndicatorColor().withValues(alpha: 0.7), // Light version of active color
                      indicatorColor: _getIndicatorColor(),
                      indicatorWeight: 3,
                      tabs: [
                        Tab(
                          icon: Icon(
                            Icons.today,
                            color: _tabController.index == 0
                                ? _primaryColor // Active: Full green
                                : _primaryColor.withValues(alpha: 0.7), // Inactive: Light green
                          ),
                          child: Text(
                            'Daily',
                            style: TextStyle(
                              color: _tabController.index == 0
                                  ? _primaryColor // Active: Full green
                                  : _primaryColor.withValues(alpha: 0.7), // Inactive: Light green
                              fontWeight: _tabController.index == 0 ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ),
                        Tab(
                          icon: Icon(
                            Icons.date_range,
                            color: _tabController.index == 1
                                ? _secondaryColor // Active: Full blue
                                : _secondaryColor.withValues(alpha: 0.7), // Inactive: Light blue
                          ),
                          child: Text(
                            'Weekly',
                            style: TextStyle(
                              color: _tabController.index == 1
                                  ? _secondaryColor // Active: Full blue
                                  : _secondaryColor.withValues(alpha: 0.7), // Inactive: Light blue
                              fontWeight: _tabController.index == 1 ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ),
                        Tab(
                          icon: Icon(
                            Icons.calendar_month,
                            color: _tabController.index == 2
                                ? _accentColor // Active: Full purple
                                : _accentColor.withValues(alpha: 0.7), // Inactive: Light purple
                          ),
                          child: Text(
                            'Monthly',
                            style: TextStyle(
                              color: _tabController.index == 2
                                  ? _accentColor // Active: Full purple
                                  : _accentColor.withValues(alpha: 0.7), // Inactive: Light purple
                              fontWeight: _tabController.index == 2 ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Filter Section
                  _buildFilterSection(),
                  // Tab Content
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        // Daily Tab
                        DailyTab(
                          filteredRecords: filteredRecords,
                          cattleMap: _cattleMap,
                          animalTypeMap: _animalTypeMap,
                          selectedDate: _selectedDate,
                          selectDate: _selectDate,
                        ),
                        // Weekly Tab
                        WeeklyTab(
                          filteredRecords: filteredRecords,
                          cattleMap: _cattleMap,
                          animalTypeMap: _animalTypeMap,
                          selectedWeek: _selectedWeek,
                          selectWeek: _selectWeek,
                        ),
                        // Monthly Tab
                        MonthlyTab(
                          filteredRecords: filteredRecords,
                          cattleMap: _cattleMap,
                          animalTypeMap: _animalTypeMap,
                          selectedMonth: _selectedMonth,
                          selectMonth: _selectMonth,
                          onMonthChanged: (month) {
                            _safeSetState(() {
                              _selectedMonth = month;
                            });
                          },
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }
}
