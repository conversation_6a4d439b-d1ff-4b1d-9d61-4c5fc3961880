import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import '../models/farm_isar.dart';
import 'package:uuid/uuid.dart';
import 'dart:async';
import 'package:logging/logging.dart';
import '../../../utils/message_utils.dart';

class FarmFormDialog extends StatefulWidget {
  final FarmIsar? farm;

  const FarmFormDialog({Key? key, this.farm}) : super(key: key);

  @override
  State<FarmFormDialog> createState() => _FarmFormDialogState();
}

class _FarmFormDialogState extends State<FarmFormDialog> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _ownerNameController = TextEditingController();
  final _ownerContactController = TextEditingController();
  final _ownerEmailController = TextEditingController();
  final _addressController = TextEditingController();
  final _cattleCountController = TextEditingController();
  final _capacityController = TextEditingController();
  final _logger = Logger('FarmFormDialog');

  FarmType _selectedFarmType = FarmType.mixed;
  double? _latitude;
  double? _longitude;
  bool _isFetchingLocation = false;

  void _showError(String message) {
    if (!mounted) return;
    FarmSetupMessageUtils.showError(context, message);
  }

  @override
  void initState() {
    super.initState();
    if (widget.farm != null) {
      _nameController.text = widget.farm!.name ?? '';
      _ownerNameController.text = widget.farm!.ownerName ?? '';
      _ownerContactController.text = widget.farm!.ownerContact ?? '';
      _ownerEmailController.text = widget.farm!.ownerEmail ?? '';
      _addressController.text = widget.farm!.address ?? '';
      _cattleCountController.text = widget.farm!.cattleCount?.toString() ?? '0';
      _capacityController.text = widget.farm!.capacity?.toString() ?? '0';
      _selectedFarmType = widget.farm!.farmType;
      _latitude = widget.farm!.latitude;
      _longitude = widget.farm!.longitude;
    } else {
      _cattleCountController.text = '0';
      _capacityController.text = '0';
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _ownerNameController.dispose();
    _ownerContactController.dispose();
    _ownerEmailController.dispose();
    _addressController.dispose();
    _cattleCountController.dispose();
    _capacityController.dispose();
    super.dispose();
  }

  Future<void> _getCurrentLocation() async {
    if (_isFetchingLocation) return; // Prevent concurrent requests

    setState(() => _isFetchingLocation = true);

    try {
      _logger.info("Starting location retrieval process");

      // Check if location services are enabled
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      _logger.info("Location services enabled: $serviceEnabled");

      if (!serviceEnabled) {
        // Location services are not enabled
        _showError(
            'Location services are disabled. Please enable location services.');
        return;
      }

      // Check location permission
      _logger.info("Checking location permission");
      LocationPermission permission = await Geolocator.checkPermission();
      _logger.info("Current permission status: $permission");

      if (permission == LocationPermission.denied ||
          permission == LocationPermission.unableToDetermine) {
        _logger.info(
            "Permission denied or unable to determine, requesting permission");
        permission = await Geolocator.requestPermission();
        _logger.info("After request, permission status: $permission");

        if (permission == LocationPermission.denied) {
          _showError(
              'Location permission denied. Please grant access in Settings.');
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        _showError(
            'Location permission permanently denied. Please enable in Settings > Apps > Cattle Manager.');
        return;
      }

      _logger.info("Permission granted, getting current position");

      // Use a simpler approach with less timeout complexity
      try {
        final position = await Geolocator.getCurrentPosition(
          desiredAccuracy: LocationAccuracy.medium,
          timeLimit: const Duration(seconds: 10),
        );

        if (!mounted) return;

        _logger.info(
            "Position obtained: ${position.latitude}, ${position.longitude}");
        setState(() {
          _latitude = position.latitude;
          _longitude = position.longitude;
        });

        // Try reverse geocoding to get address from coordinates
        try {
          _logger.info("Starting reverse geocoding");
          List<Placemark> placemarks = await placemarkFromCoordinates(
            position.latitude,
            position.longitude,
          ).catchError((error) {
            _logger.warning("Error in placemarkFromCoordinates: $error");
            return <Placemark>[];
          });

          if (placemarks.isNotEmpty) {
            Placemark place = placemarks[0];
            _logger.info("Raw placemark data: ${_placemarkToString(place)}");

            // Use a safer approach to format the address
            String address = _safeFormatAddress(
                place, position.latitude, position.longitude);
            _logger.info("Formatted address: $address");

            if (mounted) {
              setState(() {
                _addressController.text = address;
              });
            }
          } else {
            _logger.warning("No placemarks found for coordinates");
            if (mounted) {
              setState(() {
                _addressController.text =
                    "Location: ${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}";
              });
            }
          }
        } catch (error) {
          _logger.warning("Error in reverse geocoding: $error");
          if (mounted) {
            setState(() {
              _addressController.text =
                  "Location: ${position.latitude.toStringAsFixed(6)}, ${position.longitude.toStringAsFixed(6)}";
            });
          }
        }
      } catch (locationError) {
        _logger.warning("Error getting position: $locationError");

        // Try getting last known position as fallback
        _logger.info("Trying to get last known position instead");
        try {
          final lastPosition = await Geolocator.getLastKnownPosition();
          if (lastPosition != null && mounted) {
            _logger.info(
                "Last position obtained: ${lastPosition.latitude}, ${lastPosition.longitude}");
            setState(() {
              _latitude = lastPosition.latitude;
              _longitude = lastPosition.longitude;
            });

            // Try reverse geocoding with last known position
            try {
              List<Placemark> placemarks = await placemarkFromCoordinates(
                lastPosition.latitude,
                lastPosition.longitude,
              ).catchError((error) {
                _logger.warning(
                    "Error in placemarkFromCoordinates for last position: $error");
                return <Placemark>[];
              });

              if (placemarks.isNotEmpty && mounted) {
                Placemark place = placemarks[0];
                _logger.info(
                    "Last position placemark data: ${_placemarkToString(place)}");

                // Use the safer method to format address
                String address = _safeFormatAddress(
                    place, lastPosition.latitude, lastPosition.longitude);
                _logger.info("Last position formatted address: $address");

                setState(() {
                  _addressController.text = address;
                });
              } else if (mounted) {
                setState(() {
                  _addressController.text =
                      "Location: ${lastPosition.latitude.toStringAsFixed(6)}, ${lastPosition.longitude.toStringAsFixed(6)}";
                });
              }
            } catch (e) {
              _logger.warning(
                  "Error in reverse geocoding last known position: $e");
              if (mounted) {
                setState(() {
                  _addressController.text =
                      "Location: ${lastPosition.latitude.toStringAsFixed(6)}, ${lastPosition.longitude.toStringAsFixed(6)}";
                });
              }
            }

            _showError(
                'Using last known location. For better accuracy, try again outdoors.');
          } else {
            throw Exception("No location available");
          }
        } catch (e) {
          _logger.severe("Failed to get last known position: $e");
          _showError(
              'Could not determine location. Please try again or enter address manually.');
        }
      }
    } catch (e) {
      _logger.severe("Error in _getCurrentLocation: $e");
      _showError('Error getting location. Please try again later.');
    } finally {
      if (mounted) {
        setState(() => _isFetchingLocation = false);
      }
    }
  }

  // Helper method to format address from Placemark

  // Helper method to safely format address from Placemark with fallback
  String _safeFormatAddress(
      Placemark place, double latitude, double longitude) {
    // Create a list to hold non-empty address components
    List<String> addressParts = [];

    // Try to add each non-null and non-empty component
    // Using safe access pattern for each property
    if (place.name != null && place.name!.isNotEmpty) {
      addressParts.add(place.name!);
    }

    if (place.street != null && place.street!.isNotEmpty) {
      addressParts.add(place.street!);
    }

    if (place.subLocality != null && place.subLocality!.isNotEmpty) {
      addressParts.add(place.subLocality!);
    }

    if (place.locality != null && place.locality!.isNotEmpty) {
      addressParts.add(place.locality!);
    }

    if (place.administrativeArea != null &&
        place.administrativeArea!.isNotEmpty) {
      addressParts.add(place.administrativeArea!);
    }

    if (place.postalCode != null && place.postalCode!.isNotEmpty) {
      addressParts.add(place.postalCode!);
    }

    if (place.country != null && place.country!.isNotEmpty) {
      addressParts.add(place.country!);
    }

    // If we got address parts, return them joined by commas
    if (addressParts.isNotEmpty) {
      return addressParts.join(', ');
    }

    // Fallback to coordinates if no address parts found
    return "Location: ${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}";
  }

  // Helper to create a readable string of all placemark fields for debugging
  String _placemarkToString(Placemark place) {
    return '''
    name: ${place.name}, 
    street: ${place.street}, 
    isoCountryCode: ${place.isoCountryCode}, 
    country: ${place.country}, 
    postalCode: ${place.postalCode}, 
    administrativeArea: ${place.administrativeArea}, 
    subAdministrativeArea: ${place.subAdministrativeArea}, 
    locality: ${place.locality}, 
    subLocality: ${place.subLocality}, 
    thoroughfare: ${place.thoroughfare}, 
    subThoroughfare: ${place.subThoroughfare}
    ''';
  }

  Future<void> _handleSubmit() async {
    if (!_formKey.currentState!.validate()) return;

    try {
      FarmIsar farmToSave;

      if (widget.farm != null) {
        // Editing existing farm - create a new object but preserve the Isar ID
        farmToSave = FarmIsar.create(
          id: widget.farm!.farmBusinessId ?? const Uuid().v4(),
          name: _nameController.text,
          ownerName: _ownerNameController.text,
          ownerContact: _ownerContactController.text,
          ownerEmail: _ownerEmailController.text,
          latitude: _latitude,
          longitude: _longitude,
          address: _addressController.text,
          farmType: _selectedFarmType,
          cattleCount: int.tryParse(_cattleCountController.text) ??
              widget.farm!.cattleCount ??
              0,
          capacity: int.tryParse(_capacityController.text) ??
              widget.farm!.capacity ??
              0,
          createdAt: widget.farm!.createdAt ?? DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Preserve the internal Isar ID for update
        farmToSave.id = widget.farm!.id;
      } else {
        // Creating a new farm
        farmToSave = FarmIsar.create(
          id: const Uuid().v4(), // This should map to farmBusinessId
          name: _nameController.text,
          ownerName: _ownerNameController.text,
          ownerContact: _ownerContactController.text,
          ownerEmail: _ownerEmailController.text,
          latitude: _latitude,
          longitude: _longitude,
          address: _addressController.text,
          farmType: _selectedFarmType,
          cattleCount: int.tryParse(_cattleCountController.text) ?? 0,
          capacity: int.tryParse(_capacityController.text) ?? 0,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }

      if (!mounted) return;
      Navigator.of(context).pop(farmToSave);
    } catch (e) {
      _logger.severe("Error creating/updating farm: $e");
      _showError(
          'Failed to save farm details. Please check your inputs and try again.');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.farm == null ? 'Add New Farm' : 'Edit Farm',
                style: theme.textTheme.titleLarge?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _nameController,
                decoration: InputDecoration(
                  labelText: 'Farm Name',
                  border: const OutlineInputBorder(),
                  prefixIcon: Icon(Icons.business, color: colorScheme.primary),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter farm name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _ownerNameController,
                decoration: InputDecoration(
                  labelText: 'Owner Name',
                  border: const OutlineInputBorder(),
                  prefixIcon: Icon(Icons.person, color: colorScheme.primary),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter owner name';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _ownerContactController,
                decoration: InputDecoration(
                  labelText: 'Owner Contact',
                  border: const OutlineInputBorder(),
                  prefixIcon: Icon(Icons.phone, color: colorScheme.primary),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter owner contact';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _ownerEmailController,
                decoration: InputDecoration(
                  labelText: 'Owner Email',
                  border: const OutlineInputBorder(),
                  prefixIcon: Icon(Icons.email, color: colorScheme.primary),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter owner email';
                  }
                  if (!value.contains('@')) {
                    return 'Please enter a valid email';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _addressController,
                      decoration: InputDecoration(
                        labelText: 'Address',
                        border: const OutlineInputBorder(),
                        prefixIcon:
                            Icon(Icons.home, color: colorScheme.primary),
                      ),
                    ),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton.icon(
                    onPressed: _isFetchingLocation ? null : _getCurrentLocation,
                    style: ElevatedButton.styleFrom(
                      foregroundColor: colorScheme.onPrimary,
                      backgroundColor: colorScheme.primary,
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                    ),
                    icon: _isFetchingLocation
                        ? SizedBox(
                            width: 18,
                            height: 18,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              color: colorScheme.onPrimary,
                            ),
                          )
                        : const Icon(Icons.location_on),
                    label: Text(
                        _isFetchingLocation ? 'Getting...' : 'Get Location'),
                  ),
                ],
              ),
              if (_latitude != null && _longitude != null)
                Padding(
                  padding: const EdgeInsets.only(top: 8.0),
                  child: Text(
                    'GPS: ${_latitude!.toStringAsFixed(6)}, ${_longitude!.toStringAsFixed(6)}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              const SizedBox(height: 16),
              DropdownButtonFormField<FarmType>(
                value: _selectedFarmType,
                decoration: InputDecoration(
                  labelText: 'Farm Type',
                  border: const OutlineInputBorder(),
                  prefixIcon: Icon(Icons.category, color: colorScheme.primary),
                ),
                items: FarmType.values.map((type) {
                  return DropdownMenuItem(
                    value: type,
                    child: Text(type.toString().split('.').last.toUpperCase()),
                  );
                }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _selectedFarmType = value;
                    });
                  }
                },
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      controller: _cattleCountController,
                      decoration: InputDecoration(
                        labelText: 'Current Cattle Count',
                        border: const OutlineInputBorder(),
                        prefixIcon:
                            Icon(Icons.numbers, color: colorScheme.primary),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter cattle count';
                        }
                        if (int.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      controller: _capacityController,
                      decoration: InputDecoration(
                        labelText: 'Farm Capacity',
                        border: const OutlineInputBorder(),
                        prefixIcon:
                            Icon(Icons.domain, color: colorScheme.primary),
                      ),
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        if (value == null || value.isEmpty) {
                          return 'Please enter farm capacity';
                        }
                        if (int.tryParse(value) == null) {
                          return 'Please enter a valid number';
                        }
                        return null;
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Navigator.pop(context),
                    style: TextButton.styleFrom(
                      foregroundColor: colorScheme.primary,
                    ),
                    child: const Text('CANCEL'),
                  ),
                  const SizedBox(width: 16),
                  ElevatedButton(
                    onPressed: _handleSubmit,
                    style: ElevatedButton.styleFrom(
                      foregroundColor: colorScheme.onPrimary,
                      backgroundColor: colorScheme.primary,
                    ),
                    child: Text(widget.farm == null ? 'ADD' : 'SAVE'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
