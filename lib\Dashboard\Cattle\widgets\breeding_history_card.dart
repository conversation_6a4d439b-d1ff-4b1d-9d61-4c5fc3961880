import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'info_row.dart';

class BreedingHistoryCard extends StatelessWidget {
  final List<Map<String, dynamic>> records;
  final String title;
  final String emptyMessage;
  final Function(Map<String, dynamic>)? onEdit;
  final Function(Map<String, dynamic>)? onDelete;
  final Function(Map<String, dynamic>)? onStatusTap;
  final Function(Map<String, dynamic>)? onCattleTap; // Add cattle tap callback
  // Optional cattle information - if provided, it will be shown in record headers
  final String? cattleName;
  final String? cattleId;

  const BreedingHistoryCard({
    super.key,
    required this.records,
    this.title = 'Breeding History',
    this.emptyMessage = 'No breeding records found',
    this.onEdit,
    this.onDelete,
    this.onStatusTap,
    this.onCattleTap, // Add cattle tap callback
    this.cattleName,
    this.cattleId,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      color: Colors.white,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          // Use custom header implementation like DeliveryHistoryCard
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: const Color(0xFF2E7D32)
                  .withAlpha(26), // 0.1 opacity = 26 alpha
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: const Color(0xFF2E7D32)
                      .withAlpha(51), // 0.2 opacity = 51 alpha
                  child: const Icon(
                    Icons.history,
                    color: Color(0xFF2E7D32),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFF2E7D32),
                  ),
                ),
              ],
            ),
          ),

          // Content based on whether records are available
          if (records.isEmpty)
            Padding(
              padding: const EdgeInsets.all(24.0),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: const Color(0xFF2E7D32).withAlpha(26), // 0.1 * 255 = 26
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.history_outlined,
                        size: 48,
                        color: Color(0xFF2E7D32),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      emptyMessage,
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Add a breeding record to start tracking',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.black54,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
            )
          else
            Flexible(
              child: Padding(
                padding: const EdgeInsets.fromLTRB(10, 16, 10, 16),
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: records.length,
                  itemBuilder: (context, index) {
                    return _buildBreedingRecord(context, records[index]);
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.purple; // Purple
      case 'confirmed':
        return Colors.green; // Green
      case 'pending':
        return Colors.blue; // Blue
      case 'failed':
        return Colors.red; // Red
      default:
        return const Color(0xFF795548); // Brown - for unknown breeding status
    }
  }

  Widget _buildBreedingRecord(
      BuildContext context, Map<String, dynamic> record) {
    final breedingDate = DateTime.parse(record['date']);
    final status = record['status']?.toString() ?? 'Unknown';
    final statusColor = _getStatusColor(status);
    final method = record['method']?.toString() ?? 'Not specified';
    final bullOrSemen = record['bullIdOrType']?.toString() ?? 'Not specified';
    final cost = record['cost'] != null
        ? '\$${(num.tryParse(record['cost'].toString()) ?? 0.0).toStringAsFixed(2)}'
        : '\$0.00';
    final expectedDate = record['expectedDate'] != null
        ? DateTime.parse(record['expectedDate'].toString())
        : null;
    final completionDate = record['completionDate'] != null
        ? DateTime.parse(record['completionDate'].toString())
        : null;
    final notes = record['notes']?.toString() ?? '';

    // Use record's cattle info with fallback to the widget's cattle info
    final recordCattleId = record['cattleId']?.toString() ?? '';
    final recordCattleName = record['cattleName']?.toString() ?? '';
    final displayCattleId =
        recordCattleId.isNotEmpty ? recordCattleId : cattleId ?? '';
    final displayCattleName =
        recordCattleName.isNotEmpty ? recordCattleName : cattleName ?? '';

    // Use the color directly instead of converting to hex and back
    final statusColorFromHex = statusColor;

    // Create a unique key for this record to ensure rebuilds when data changes
    final recordKey =
        ValueKey('${record['id']}_${record['date']}_${record['status']}');

    // Simple divider between records
    if (records.indexOf(record) > 0) {
      return Column(
        key: recordKey,
        children: [
          const Divider(height: 32),
          _buildRecordContent(
            context,
            record,
            breedingDate,
            status,
            statusColorFromHex,
            method,
            bullOrSemen,
            cost,
            expectedDate,
            completionDate,
            notes,
            displayCattleId,
            displayCattleName,
          ),
        ],
      );
    }

    return _buildRecordContent(
      context,
      record,
      breedingDate,
      status,
      statusColorFromHex,
      method,
      bullOrSemen,
      cost,
      expectedDate,
      completionDate,
      notes,
      displayCattleId,
      displayCattleName,
      key: recordKey,
    );
  }

  Widget _buildRecordContent(
      BuildContext context,
      Map<String, dynamic> record,
      DateTime breedingDate,
      String status,
      Color statusColorFromHex,
      String method,
      String bullOrSemen,
      String cost,
      DateTime? expectedDate,
      DateTime? completionDate,
      String notes,
      String displayCattleId,
      String displayCattleName,
      {Key? key}) {
    return Column(
      key: key,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header with date and status - make entire header tappable
        GestureDetector(
          onTap: onCattleTap != null ? () => onCattleTap!(record) : null,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: statusColorFromHex.withAlpha(51),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Row(
                    children: [
                      CircleAvatar(
                        radius: 20,
                        backgroundColor: statusColorFromHex.withAlpha(70),
                        child: Icon(
                          Icons.favorite,
                          color: statusColorFromHex,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              DateFormat('MMMM dd, yyyy').format(breedingDate),
                              style: const TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                              overflow: TextOverflow.visible,
                            ),
                            if (displayCattleId.isNotEmpty ||
                                displayCattleName.isNotEmpty)
                              Text(
                                '${displayCattleName.isNotEmpty ? displayCattleName : 'Unknown'} (${displayCattleId.isNotEmpty ? displayCattleId : 'Unknown'})',
                                style: TextStyle(
                                  fontSize: 15,
                                  fontWeight: FontWeight.w600,
                                  color: statusColorFromHex,
                                ),
                                overflow: TextOverflow.visible,
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                // Options menu
                if (onEdit != null || onDelete != null)
                  PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert),
                    tooltip: 'More options',
                    onSelected: (value) {
                      if (value == 'edit' && onEdit != null) {
                        onEdit!(record);
                      } else if (value == 'delete' && onDelete != null) {
                        onDelete!(record);
                      }
                    },
                    itemBuilder: (context) => [
                      if (onEdit != null)
                        const PopupMenuItem(
                          value: 'edit',
                          child: Row(
                            children: [
                              Icon(Icons.edit),
                              SizedBox(width: 8),
                              Text('Edit'),
                            ],
                          ),
                        ),
                      if (onDelete != null)
                        const PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete, color: Colors.red),
                              SizedBox(width: 8),
                              Text('Delete', style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                    ],
                  ),
              ],
            ),
          ),
        ),

        // Content section
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Info rows
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status (moved from header)
                  InfoRow(
                    icon: Icons.check_circle_outline,
                    label: 'Status',
                    value: status,
                    color: statusColorFromHex,
                    isStatus: true,
                    onTap:
                        onStatusTap != null ? () => onStatusTap!(record) : null,
                  ),
                  const SizedBox(height: 12),
                  // Method
                  InfoRow(
                    icon: Icons.science_outlined,
                    label: 'Method',
                    value: method,
                    color: Colors.purple,
                  ),
                  const SizedBox(height: 12),
                  // Bull/Semen
                  InfoRow(
                    icon: Icons.local_offer_outlined,
                    label: 'Bull/Semen',
                    value: bullOrSemen,
                    color: Colors.teal,
                  ),
                  const SizedBox(height: 12),
                  // Cost
                  InfoRow(
                    icon: Icons.monetization_on_outlined,
                    label: 'Cost',
                    value: cost,
                    color: Colors.indigo,
                  ),
                  const SizedBox(height: 12),
                  // Delivery date with conditional logic
                  InfoRow(
                    icon: status.toLowerCase() == 'completed'
                        ? Icons.baby_changing_station
                        : Icons.event_available,
                    label: status.toLowerCase() == 'completed'
                        ? 'Delivery Date'
                        : 'Expected Delivery',
                    value: status.toLowerCase() == 'completed' &&
                            completionDate != null
                        ? DateFormat('MMM dd, yyyy').format(completionDate)
                        : expectedDate != null
                            ? DateFormat('MMM dd, yyyy').format(expectedDate)
                            : 'Not calculated',
                    color: Colors.teal,
                    isHighlighted: false,
                  ),
                ],
              ),

              // Notes section
              if (notes.isNotEmpty) ...[
                const SizedBox(height: 12),
                const Divider(),
                const SizedBox(height: 4),
                InfoRow(
                  icon: Icons.notes,
                  label: 'Notes',
                  value: notes,
                  color: Colors.deepPurple,
                  isMultiline: true,
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
