import 'package:flutter/material.dart';

class AppTheme {
  // Primary Colors
  static const Color primaryColor =
      Color(0xFF2E7D32); // Dark green for app bar and buttons

  // Background Colors
  static const Color scaffoldBackground =
      Color(0xFFF8F9FA); // Very light blue-grey for screen backgrounds
  static const Color cardBackground = Colors.white; // White for cards

  // Text Colors
  static const Color primaryText = Colors.black87;
  static const Color secondaryText = Colors.black54;

  // Icon Colors
  static const Color iconColor = Color(0xFF2E7D32); // Dark green for icons
  static const Color actionButtonColor =
      Color(0xFFFF8C00); // Orange for action buttons like sync

  // Border Radius
  static final BorderRadius cardRadius = BorderRadius.circular(8.0);
  static final BorderRadius buttonRadius = BorderRadius.circular(8.0);

  // Padding
  static const EdgeInsets screenPadding = EdgeInsets.all(16.0);
  static const EdgeInsets cardPadding = EdgeInsets.all(16.0);

  // Card Decoration
  static final BoxDecoration cardDecoration = BoxDecoration(
    color: cardBackground,
    borderRadius: cardRadius,
  );

  // Button Style
  static final ButtonStyle primaryButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: primaryColor,
    foregroundColor: Colors.white,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(borderRadius: buttonRadius),
  );

  static final ButtonStyle actionButtonStyle = ElevatedButton.styleFrom(
    backgroundColor: actionButtonColor,
    foregroundColor: Colors.white,
    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
    shape: RoundedRectangleBorder(borderRadius: buttonRadius),
  );

  // Text Styles
  static const TextStyle titleStyle = TextStyle(
    fontSize: 18,
    fontWeight: FontWeight.bold,
    color: primaryText,
  );

  static const TextStyle subtitleStyle = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w500,
    color: secondaryText,
  );

  // App Bar Theme
  static const AppBarTheme appBarTheme = AppBarTheme(
    backgroundColor: primaryColor,
    foregroundColor: Colors.white,
    elevation: 0,
  );
}
