import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import 'package:logging/logging.dart';
import '../../../routes/app_routes.dart';
import '../dialogs/health_record_form_dialog.dart';
import '../../../services/database/database_helper.dart';
import '../../../services/streams/stream_service.dart';
import '../../../utils/message_utils.dart';
import 'health_records_list.dart';
import 'vaccinations_screen.dart';
import 'treatments_screen.dart';
import '../services/health_scheduler_service.dart';
import '../widgets/health_alerts_widget.dart';

class HealthScreen extends StatefulWidget {
  const HealthScreen({super.key});

  @override
  State<HealthScreen> createState() => _HealthScreenState();
}

class _HealthScreenState extends State<HealthScreen> {
  static final Logger _logger = Logger('HealthScreen');
  late DatabaseHelper _dbHelper;
  bool _isLoading = true;
  int _cattleCount = 0;
  int _healthRecordsCount = 0;
  int _vaccinationsCount = 0;
  int _treatmentsCount = 0;
  int _activeRecordsCount = 0;
  int _completedRecordsCount = 0;
  int _unreadAlertsCount = 0;

  // Stream subscriptions for real-time updates
  StreamSubscription<Map<String, dynamic>>? _healthStreamSubscription;
  StreamSubscription<Map<String, dynamic>>? _vaccinationStreamSubscription;
  StreamSubscription<Map<String, dynamic>>? _treatmentStreamSubscription;

  @override
  void initState() {
    super.initState();
    _dbHelper = DatabaseHelper.instance;
    _loadData();
    _subscribeToHealthUpdates();
  }

  @override
  void dispose() {
    _healthStreamSubscription?.cancel();
    _vaccinationStreamSubscription?.cancel();
    _treatmentStreamSubscription?.cancel();
    super.dispose();
  }

  Future<void> _loadData() async {
    try {
      setState(() => _isLoading = true);

      final allCattle = await _dbHelper.cattleHandler.getAllCattle();
      final allHealthRecords =
          await _dbHelper.healthHandler.getAllHealthRecords();
      final allVaccinations =
          await _dbHelper.healthHandler.getAllVaccinations();
      final allTreatments = await _dbHelper.healthHandler.getAllTreatments();

      // Process statistics
      int activeRecords = 0;
      int completedRecords = 0;

      for (var record in allHealthRecords) {
        if (record.isResolved == false) {
          activeRecords++;
        } else if (record.isResolved == true) {
          completedRecords++;
        }
      }

      // Get alert statistics
      final alertStats = await HealthSchedulerService.instance.getAlertStatistics();

      setState(() {
        _cattleCount = allCattle.length;
        _healthRecordsCount = allHealthRecords.length;
        _vaccinationsCount = allVaccinations.length;
        _treatmentsCount = allTreatments.length;
        _activeRecordsCount = activeRecords;
        _completedRecordsCount = completedRecords;
        _unreadAlertsCount = alertStats['unread'] ?? 0;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted && context.mounted) {
        HealthMessageUtils.showError(context, 'Error loading health data: $e');
      }
    }
  }

  /// Subscribe to health record updates for real-time UI updates
  void _subscribeToHealthUpdates() {
    _logger.info('🔵 Setting up health stream subscriptions');

    // Use StreamService pattern like breeding module
    final streamService = GetIt.instance<StreamService>();

    // Subscribe to health record updates
    _healthStreamSubscription = streamService.healthStream.listen((event) {
      _logger.info('🔵 Health record stream event: ${event['action']}');
      if (mounted) {
        _loadData(); // Refresh data when health records change
      }
    }, onError: (error) {
      _logger.severe('🔴 Error in health record stream: $error');
    });

    // Subscribe to vaccination updates
    _vaccinationStreamSubscription = streamService.vaccinationStream.listen((event) {
      _logger.info('🔵 Vaccination stream event: ${event['action']}');
      if (mounted) {
        _loadData(); // Refresh data when vaccinations change
      }
    }, onError: (error) {
      _logger.severe('🔴 Error in vaccination stream: $error');
    });

    // Subscribe to treatment updates
    _treatmentStreamSubscription = streamService.treatmentStream.listen((event) {
      _logger.info('🔵 Treatment stream event: ${event['action']}');
      if (mounted) {
        _loadData(); // Refresh data when treatments change
      }
    }, onError: (error) {
      _logger.severe('🔴 Error in treatment stream: $error');
    });
  }

  @override
  Widget build(BuildContext context) {
    const mainColor = Color(0xFF2E7D32);

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          'Health Management',
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: mainColor,
        elevation: 0,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: Colors.white),
            onPressed: _generateAlertsManually,
            tooltip: 'Generate Health Alerts',
          ),
          IconButton(
            icon: const Icon(Icons.bar_chart, color: Colors.white),
            onPressed: () => Navigator.pushNamed(
              context,
              AppRoutes.healthReport,
            ),
            tooltip: 'View Health Reports',
          ),
        ],
      ),
      backgroundColor: Colors.grey[50],
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          // Fetch cattle data from database using DatabaseHelper
          final dbHelper = DatabaseHelper.instance;
          final allCattle = await dbHelper.cattleHandler.getAllCattle();

          if (!context.mounted) return;

          if (allCattle.isEmpty) {
            // Handle the empty case, e.g., show a message or return
            return;
          }

          // Get the first cattle's tag ID with null safety

          _logger.info('🔵 Opening health record form dialog');
          showDialog(
            context: context,
            builder: (context) => HealthRecordFormDialog(
              cattle: allCattle,
            ),
            // Handle the result when dialog is closed
          ).then((record) async {
            _logger.info('🔵 Dialog closed, record: ${record != null ? 'received' : 'null'}');
            if (record != null) {
              _logger.info('🔵 Saving health record via dbHelper.healthHandler.addOrUpdateHealthRecord');
              try {
                // Handle saving the record
                await _dbHelper.healthHandler.addOrUpdateHealthRecord(record);
                _logger.info('🟢 Health record saved successfully');
                if (mounted) {
                  _loadData(); // Refresh data
                  if (context.mounted) {
                    HealthMessageUtils.showSuccess(context,
                        HealthMessageUtils.healthRecordCreated());
                  }
                }
              } catch (e, stackTrace) {
                _logger.severe('🔴 Error saving health record in HealthScreen: $e');
                _logger.severe('🔴 Stack trace: $stackTrace');
                if (mounted && context.mounted) {
                  HealthMessageUtils.showError(context, 'Failed to save health record: $e');
                }
              }
            }
          });
        },
        backgroundColor: mainColor,
        child: const Icon(Icons.add),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildStatisticsSection(),
                    const SizedBox(height: 24),
                    // Add health alerts widget
                    HealthAlertsWidget(
                      showAllAlerts: false,
                      maxAlertsToShow: 5,
                    ),
                    const SizedBox(height: 24),
                    _buildMainOptionsGrid(),
                  ],
                ),
              ),
            ),
    );
  }

  Widget _buildStatisticsSection() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Health Overview',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  'Cattle',
                  _cattleCount.toString(),
                  Icons.pets,
                  Colors.brown,
                ),
                _buildStatItem(
                  'Health Records',
                  _healthRecordsCount.toString(),
                  Icons.medical_services,
                  Colors.blue,
                ),
                _buildStatItem(
                  'Vaccinations',
                  _vaccinationsCount.toString(),
                  Icons.medication_liquid,
                  Colors.teal,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(
                  'Treatments',
                  _treatmentsCount.toString(),
                  Icons.healing,
                  Colors.indigo,
                ),
                _buildStatItem(
                  'Active',
                  _activeRecordsCount.toString(),
                  Icons.warning,
                  Colors.red,
                ),
                _buildStatItem(
                  'Completed',
                  _completedRecordsCount.toString(),
                  Icons.check_circle,
                  Colors.green,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                _buildStatItem(
                  'Unread Alerts',
                  _unreadAlertsCount.toString(),
                  Icons.notifications_active,
                  _unreadAlertsCount > 0 ? Colors.orange : const Color(0xFF607D8B), // Blue Grey when no alerts
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: color.withAlpha(25),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            icon,
            color: color,
            size: 28,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
          ),
        ),
      ],
    );
  }

  Widget _buildMainOptionsGrid() {
    return GridView.count(
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      children: [
        _buildOptionCard(
          title: 'Health Records',
          icon: Icons.medical_services,
          color: Colors.blue,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const HealthRecordsList(),
              ),
            );
          },
        ),
        _buildOptionCard(
          title: 'Vaccinations',
          icon: Icons.medication_liquid,
          color: Colors.teal,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const VaccinationsScreen(),
              ),
            );
          },
        ),
        _buildOptionCard(
          title: 'Treatments',
          icon: Icons.healing,
          color: Colors.indigo,
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => const TreatmentsScreen(),
              ),
            );
          },
        ),
        _buildOptionCard(
          title: 'Health Reports',
          icon: Icons.summarize,
          color: Colors.purple,
          onTap: () {
            // Health reports screen
          },
        ),
      ],
    );
  }

  Widget _buildOptionCard({
    required String title,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: color.withAlpha(25),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 40,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Manually generate health alerts
  Future<void> _generateAlertsManually() async {
    try {
      // Show loading indicator
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => const AlertDialog(
          content: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text('Generating health alerts...'),
            ],
          ),
        ),
      );

      // Generate alerts
      await HealthSchedulerService.instance.generateAlertsNow();

      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Reload data to update alert count
      await _loadData();

      // Show success message
      if (mounted) {
        HealthMessageUtils.showSuccess(context, 'Health alerts generated successfully');
      }
    } catch (e) {
      // Close loading dialog
      if (mounted) Navigator.of(context).pop();

      // Show error message
      if (mounted) {
        HealthMessageUtils.showError(context, 'Error generating alerts: $e');
      }
    }
  }
}
